python -u train_net.py `
    --GPU=0 `
    --DATASET=STEEL `
    --DATASET_PATH=./datasets/STEEL/ `
    --RUN_NAME=ALL_3000_N_3000 `
    --RESULTS_PATH=./results `
    --SAVE_IMAGES=True `
    --DILATE=1 `
    --TRAIN_NUM=3000 `
    --NUM_SEGMENTED=3000 `
    --EPOCHS=40 `
    --LEARNING_RATE=0.1 `
    --DELTA_CLS_LOSS=0.1 `
    --BATCH_SIZE=10 `
    --WEIGHTED_SEG_LOSS=True `
    --WEIGHTED_SEG_LOSS_P=2 `
    --WEIGHTED_SEG_LOSS_MAX=1 `
    --DYN_BALANCED_LOSS=True `
    --GRADIENT_ADJUSTMENT=True `
    --FREQUENCY_SAMPLING=True `
    --VALIDATE=True