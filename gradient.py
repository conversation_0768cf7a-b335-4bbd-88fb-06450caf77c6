import cv2
import numpy as np
import math
import json
from typing import List, Tuple, Optional, Dict, Any
from pathlib import Path
from scipy.spatial import KDTree


class Defect:
    def __init__(self):
        self.center = (0, 0)  # 原图坐标系
        self.radius = 0
        self.is_bright = False
        self.contrast = 0.0
        self.guide_id = -1  # 所属光导ID

    def __repr__(self):
        return (f"{'亮斑' if self.is_bright else '暗斑'}: 光导{self.guide_id}，中心{self.center}, "
                f"半径{self.radius:.2f}, 对比度{self.contrast:.2f}")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典用于JSON序列化"""
        return {
            "type": "bright" if self.is_bright else "dark",
            "center_x": self.center[0],
            "center_y": self.center[1],
            "radius": self.radius,
            "contrast": self.contrast,
            "guide_id": self.guide_id
        }


class DetectionConfig:
    """检测参数配置类，集中管理所有可配置参数"""

    def __init__(self):
        # 基础检测参数
        self.threshold_factor = 2.0
        self.min_defect_area = 20
        self.min_circularity = 0.3
        self.light_guide_min_area = 5000
        self.segment_length = 150
        self.min_segment_size = 10

        # 缺陷合并参数
        self.iou_thresh = 0.25
        self.dist_ratio = 0.6

        # 预处理参数
        self.use_clahe = True  # 是否使用CLAHE光照补偿
        self.clahe_clip_limit = 2.0
        self.clahe_grid_size = (8, 8)
        self.blur_kernel = (5, 5)

        # 二值化参数
        self.use_adaptive_threshold = False  # 自适应阈值/OTSU选择
        self.adaptive_block_size = 11
        self.adaptive_c = 2

        # 形态学操作参数
        self.morph_kernel_size = (5, 5)


class LightGuideDetector:
    def __init__(self, config: DetectionConfig, use_mask: bool = False,
                 mask_rects: List[Tuple[int, int, int, int]] = None):
        self.config = config
        self.use_mask = use_mask
        self.mask_rects = mask_rects if mask_rects is not None else []
        self._validate_mask_rects()
        # 用于缓存有缺陷的图像结果
        self.defective_images = []

    def _validate_mask_rects(self):
        """验证屏蔽区域参数有效性"""
        valid_rects = []
        for rect in self.mask_rects:
            x, y, w, h = rect
            if x >= 0 and y >= 0 and w > 0 and h > 0:
                valid_rects.append(rect)
            else:
                print(f"无效屏蔽区域 {rect}（x/y需非负，宽高需为正），已跳过")
        self.mask_rects = valid_rects

    def generate_global_mask(self, image_size: Tuple[int, int]) -> np.ndarray:
        """生成全局屏蔽掩码"""
        h, w = image_size
        global_mask = np.ones((h, w), dtype=np.uint8) * 255

        if not self.use_mask or not self.mask_rects:
            return global_mask

        for rect in self.mask_rects:
            x, y, width, height = rect
            x1 = max(0, x)
            y1 = max(0, y)
            x2 = min(w, x + width)
            y2 = min(h, y + height)

            if x1 >= x2 or y1 >= y2:
                print(f"屏蔽区域 {rect} 超出图像范围（图像尺寸: {w}x{h}），已跳过")
                continue

            global_mask[y1:y2, x1:x2] = 0
        return global_mask

    def generate_contour_mask(self, contour: np.ndarray, image_size: Tuple[int, int]) -> np.ndarray:
        mask = np.zeros(image_size, dtype=np.uint8)
        if len(contour.shape) == 3 and contour.shape[1] == 1:
            contour = contour.squeeze(1)
        cv2.drawContours(mask, [contour.astype(np.int32)], 0, 255, -1)
        return mask

    def detect_light_guide_contours(self, image: np.ndarray) -> List[np.ndarray]:
        """优化光导轮廓检测，增加光照补偿和自适应阈值"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if image.ndim == 3 else image.copy()
        image_size = (image.shape[0], image.shape[1])

        # 应用全局屏蔽
        if self.use_mask:
            global_mask = self.generate_global_mask(image_size)
            gray = cv2.bitwise_and(gray, gray, mask=global_mask)

        # 光照补偿：CLAHE增强对比度
        if self.config.use_clahe:
            clahe = cv2.createCLAHE(
                clipLimit=self.config.clahe_clip_limit,
                tileGridSize=self.config.clahe_grid_size
            )
            gray = clahe.apply(gray)

        # 高斯模糊降噪
        gray = cv2.GaussianBlur(gray, self.config.blur_kernel, 0)

        # 自适应二值化或OTSU二值化
        if self.config.use_adaptive_threshold:
            binary = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, self.config.adaptive_block_size, self.config.adaptive_c
            )
        else:
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 形态学操作优化轮廓
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, self.config.morph_kernel_size)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # 查找并筛选轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 基于面积筛选有效光导轮廓
        valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > self.config.light_guide_min_area]
        valid_contours.sort(key=lambda c: cv2.contourArea(c), reverse=True)

        return valid_contours

    def get_light_guide_oriented_bounds(self, contour: np.ndarray) -> Tuple[
        np.ndarray, float, Tuple[float, float], np.ndarray, np.ndarray]:
        """获取光导定向边界，用于后续分段"""
        rect = cv2.minAreaRect(contour)
        box = cv2.boxPoints(rect).reshape(4, 2)

        # 计算主轴方向
        edges = [(box[(i + 1) % 4] - box[i]) for i in range(4)]
        edge_lengths = [np.linalg.norm(e) for e in edges]
        max_idx = int(np.argmax(edge_lengths))
        vx, vy = edges[max_idx]
        angle_deg = np.degrees(np.arctan2(vy, vx))
        rot_angle = -angle_deg

        (cx, cy), (w, h), _ = rect
        major_axis = max(w, h)

        # 生成旋转矩阵及逆矩阵
        M_rot = cv2.getRotationMatrix2D((cx, cy), rot_angle, 1.0)
        try:
            M_rot_inv = cv2.invertAffineTransform(M_rot)
        except Exception as e:
            print("逆仿射计算失败，使用单位变换:", e)
            M_rot_inv = np.array([[1, 0, 0], [0, 1, 0]], dtype=np.float32)

        # 旋转轮廓点
        contour_points = contour.reshape(-1, 2).astype(np.float32)
        rotated_points = cv2.transform(contour_points[None, :, :], M_rot).squeeze(0)

        return rotated_points, major_axis, (cx, cy), M_rot, M_rot_inv

    def split_light_guide_into_segments(self, image: np.ndarray, contour: np.ndarray,
                                        full_mask: np.ndarray) -> List[
        Tuple[np.ndarray, np.ndarray, int, int, int, int, np.ndarray]]:
        """优化分段逻辑，基于光导尺寸动态调整"""
        rotated_points, major_axis, (cx, cy), M_rot, M_rot_inv = self.get_light_guide_oriented_bounds(contour)
        img_h, img_w = image.shape[:2]
        segments = []

        # 动态计算分段数量（至少1段，最多10段避免过细分段）
        num_segments = max(1, min(10, int(np.ceil(major_axis / self.config.segment_length))))
        actual_segment_length = major_axis / num_segments

        # 旋转图像和掩码（缓存结果避免重复计算）
        rotated_image = cv2.warpAffine(
            image, M_rot, (img_w, img_h),
            flags=cv2.INTER_LINEAR,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=(0, 0, 0)
        )
        rotated_mask = cv2.warpAffine(
            full_mask, M_rot, (img_w, img_h),
            flags=cv2.INTER_NEAREST,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=0
        )

        # 计算旋转后光导X轴范围
        min_x_rot = float(np.min(rotated_points[:, 0]))
        max_x_rot = float(np.max(rotated_points[:, 0]))
        center_x_rot = (min_x_rot + max_x_rot) / 2.0

        # 生成分段
        for i in range(num_segments):
            seg_start_x_rot = center_x_rot - (major_axis / 2.0) + i * actual_segment_length
            seg_end_x_rot = seg_start_x_rot + actual_segment_length

            # 筛选分段内光导点
            point_mask = (rotated_points[:, 0] >= seg_start_x_rot) & (rotated_points[:, 0] <= seg_end_x_rot)
            segment_points_rot = rotated_points[point_mask]
            if len(segment_points_rot) < 5:
                continue

            # 计算分段边界
            seg_min_x_rot = int(np.floor(np.min(segment_points_rot[:, 0])) - 2)
            seg_max_x_rot = int(np.ceil(np.max(segment_points_rot[:, 0])) + 2)
            seg_min_y_rot = int(np.floor(np.min(segment_points_rot[:, 1])) - 2)
            seg_max_y_rot = int(np.ceil(np.max(segment_points_rot[:, 1])) + 2)

            # 边界裁剪
            seg_min_x_rot = max(0, seg_min_x_rot)
            seg_max_x_rot = min(rotated_image.shape[1] - 1, seg_max_x_rot)
            seg_min_y_rot = max(0, seg_min_y_rot)
            seg_max_y_rot = min(rotated_image.shape[0] - 1, seg_max_y_rot)

            # 过滤过小分段
            seg_w_rot = seg_max_x_rot - seg_min_x_rot + 1
            seg_h_rot = seg_max_y_rot - seg_min_y_rot + 1
            if seg_w_rot < self.config.min_segment_size or seg_h_rot < self.config.min_segment_size:
                continue

            # 提取分段数据
            seg_image = rotated_image[seg_min_y_rot:seg_max_y_rot + 1, seg_min_x_rot:seg_max_x_rot + 1].copy()
            seg_mask = rotated_mask[seg_min_y_rot:seg_max_y_rot + 1, seg_min_x_rot:seg_max_x_rot + 1].copy()

            # 排除旋转产生的黑边区域（避免误检暗斑）
            non_black_mask = cv2.cvtColor(seg_image, cv2.COLOR_BGR2GRAY) > 10 if seg_image.ndim == 3 else seg_image > 10
            seg_mask = cv2.bitwise_and(seg_mask, seg_mask, mask=non_black_mask.astype(np.uint8) * 255)

            segments.append((seg_image, seg_mask, seg_min_x_rot, seg_min_y_rot, seg_w_rot, seg_h_rot, M_rot_inv))

        return segments

    def detect_bright_dark_spots(self, segment_image: np.ndarray, segment_mask: np.ndarray) -> List[Defect]:
        """优化缺陷检测逻辑，提高光照鲁棒性"""
        defects = []
        if segment_image is None or segment_mask is None or np.sum(segment_mask) < 10:
            return defects

        # 转灰度并提取ROI像素
        gray = cv2.cvtColor(segment_image, cv2.COLOR_BGR2GRAY) if segment_image.ndim == 3 else segment_image.copy()
        roi_pixels = gray[segment_mask == 255]
        if roi_pixels.size == 0:
            return defects

        # 改进的极值过滤：无论数量多少都进行过滤（使用百分位）
        p1, p99 = np.percentile(roi_pixels, [1, 99])
        roi_pixels = roi_pixels[(roi_pixels >= p1) & (roi_pixels <= p99)]
        if roi_pixels.size == 0:  # 避免过滤后为空
            roi_pixels = gray[segment_mask == 255]

        mean_val, std_val = float(np.mean(roi_pixels)), float(np.std(roi_pixels))
        if std_val < 1e-6:  # 无明显灰度变化，不检测缺陷
            return defects

        # 计算阈值
        bright_thresh = mean_val + self.config.threshold_factor * std_val
        dark_thresh = mean_val - self.config.threshold_factor * std_val

        # 生成亮暗斑掩码
        bright_mask = np.logical_and(gray > bright_thresh, segment_mask == 255).astype(np.uint8) * 255
        dark_mask = np.logical_and(gray < dark_thresh, segment_mask == 255).astype(np.uint8) * 255

        # 提取缺陷
        defects.extend(self._extract_circular_defects(bright_mask, gray, mean_val, is_bright=True))
        defects.extend(self._extract_circular_defects(dark_mask, gray, mean_val, is_bright=False))
        return defects

    def _extract_circular_defects(self, spot_mask: np.ndarray, gray: np.ndarray, mean_val: float, is_bright: bool) -> \
    List[Defect]:
        """优化缺陷提取，增加轮廓平滑处理"""
        # 形态学闭运算
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        spot_mask = cv2.morphologyEx(spot_mask, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(spot_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        defects = []

        for cnt in contours:
            # 轮廓平滑处理，减少噪声影响
            epsilon = 0.01 * cv2.arcLength(cnt, True)
            cnt = cv2.approxPolyDP(cnt, epsilon, True)

            area = cv2.contourArea(cnt)
            if area < self.config.min_defect_area:
                continue

            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0:
                continue

            # 计算圆度
            circularity = 4 * np.pi * area / (perimeter ** 2)
            if circularity < self.config.min_circularity:
                continue

            # 最小外接圆
            (x, y), radius = cv2.minEnclosingCircle(cnt)
            center = (int(round(x)), int(round(y)))
            radius = max(1, int(round(radius)))

            # 计算对比度（使用掩码区域的均值）
            mask = np.zeros_like(gray)
            cv2.circle(mask, center, radius, 255, -1)
            mask = cv2.bitwise_and(mask, spot_mask)
            spot_pixels = gray[mask == 255]
            if spot_pixels.size == 0:
                continue

            spot_mean = float(np.mean(spot_pixels))
            contrast = spot_mean - mean_val if is_bright else mean_val - spot_mean

            # 封装缺陷
            d = Defect()
            d.center, d.radius = center, radius
            d.is_bright, d.contrast = is_bright, contrast
            defects.append(d)
        return defects

    def _circle_iou(self, x1, y1, r1, x2, y2, r2):
        """计算两圆IOU"""
        d = math.hypot(x1 - x2, y1 - y2)
        if d >= r1 + r2:
            return 0.0
        if d <= abs(r1 - r2):
            return min(r1, r2) ** 2 / max(r1, r2) ** 2

        r1s, r2s = r1 * r1, r2 * r2
        alpha = math.acos(max(-1.0, min(1.0, (d * d + r1s - r2s) / (2 * d * r1))))
        beta = math.acos(max(-1.0, min(1.0, (d * d + r2s - r1s) / (2 * d * r2))))
        inter = r1s * alpha + r2s * beta - 0.5 * math.sqrt(
            max(0.0, (r1 + r2 - d) * (d + r1 - r2) * (d - r1 + r2) * (d + r1 + r2)))
        area1 = math.pi * r1s
        area2 = math.pi * r2s
        return inter / (area1 + area2 - inter)

    def merge_duplicate_defects(self, defects: List[Defect]) -> List[Defect]:
        """优化缺陷合并算法，使用KDTree加速近邻搜索"""
        n = len(defects)
        if n <= 1:
            return defects.copy()

        # 按缺陷类型分组处理
        bright_defects = [d for d in defects if d.is_bright]
        dark_defects = [d for d in defects if not d.is_bright]
        merged = []

        for group in [bright_defects, dark_defects]:
            if not group:
                continue

            # 提取特征点用于KDTree搜索
            points = np.array([[d.center[0], d.center[1]] for d in group], dtype=np.float32)
            tree = KDTree(points)

            # 并查集初始化
            parent = list(range(len(group)))

            def find(a):
                while parent[a] != a:
                    parent[a] = parent[parent[a]]  # 路径压缩
                    a = parent[a]
                return a

            def union(a, b):
                ra, rb = find(a), find(b)
                if ra != rb:
                    parent[rb] = ra

            # 搜索近邻并合并
            for i in range(len(group)):
                di = group[i]
                # 搜索半径范围内的潜在匹配（基于缺陷半径的动态搜索范围）
                search_radius = di.radius * self.config.dist_ratio
                neighbors = tree.query_ball_point(points[i], search_radius)

                for j in neighbors:
                    if i >= j:
                        continue
                    dj = group[j]
                    iou = self._circle_iou(
                        di.center[0], di.center[1], di.radius,
                        dj.center[0], dj.center[1], dj.radius
                    )
                    if iou > self.config.iou_thresh:
                        union(i, j)

            # 合并聚类结果
            clusters = {}
            for idx in range(len(group)):
                root = find(idx)
                clusters.setdefault(root, []).append(idx)

            # 计算加权平均
            for root, idxs in clusters.items():
                if len(idxs) == 1:
                    merged.append(group[idxs[0]])
                    continue

                total_area = 0.0
                weighted_x = 0.0
                weighted_y = 0.0
                weighted_contrast = 0.0
                is_bright = group[idxs[0]].is_bright

                for k in idxs:
                    d = group[k]
                    area = math.pi * (d.radius ** 2)
                    total_area += area
                    weighted_x += d.center[0] * area
                    weighted_y += d.center[1] * area
                    weighted_contrast += d.contrast * area

                if total_area <= 0:
                    continue

                cx = int(round(weighted_x / total_area))
                cy = int(round(weighted_y / total_area))
                new_radius = int(round(math.sqrt(total_area / math.pi)))
                new_contrast = float(weighted_contrast / total_area)

                md = Defect()
                md.center = (cx, cy)
                md.radius = max(1, new_radius)
                md.is_bright = is_bright
                md.contrast = new_contrast
                merged.append(md)

        return merged

    def process_light_guide_segments(self, image: np.ndarray, contour: np.ndarray,
                                     full_mask: np.ndarray, guide_id: int) -> List[Defect]:
        """处理光导分段并映射缺陷坐标"""
        guide_defects = []
        segments = self.split_light_guide_into_segments(image, contour, full_mask)
        if not segments:
            return guide_defects

        # 处理每个分段
        for seg_image, seg_mask, seg_min_x_rot, seg_min_y_rot, _, _, M_rot_inv in segments:
            # 检测缺陷
            seg_defects = self.detect_bright_dark_spots(seg_image, seg_mask)
            if not seg_defects:
                continue

            # 映射缺陷坐标到原图
            for defect in seg_defects:
                abs_rot_x = seg_min_x_rot + defect.center[0]
                abs_rot_y = seg_min_y_rot + defect.center[1]
                pt_rot = np.array([[[float(abs_rot_x), float(abs_rot_y)]]], dtype=np.float32)
                pt_orig = cv2.transform(pt_rot, M_rot_inv).squeeze()
                mapped_x = int(round(float(pt_orig[0])))
                mapped_y = int(round(float(pt_orig[1])))

                adjusted_defect = Defect()
                adjusted_defect.center = (mapped_x, mapped_y)
                adjusted_defect.radius = defect.radius
                adjusted_defect.is_bright = defect.is_bright
                adjusted_defect.contrast = defect.contrast
                adjusted_defect.guide_id = guide_id
                guide_defects.append(adjusted_defect)

        # 合并重复缺陷
        guide_defects = self.merge_duplicate_defects(guide_defects)
        return guide_defects

    def process_single_image(self, image_path: str, output_dir: str) -> Tuple[bool, Optional[str]]:
        """
        处理单张图像：检测+结果保存
        :return: (处理成功标志, 结果图像路径/None)
        """
        # 读取图像
        try:
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法读取图像: {image_path}（可能文件损坏或格式不支持）")
                return False, None
        except Exception as e:
            print(f"❌ 读取图像时发生错误 {image_path}: {str(e)}")
            return False, None

        image_name = Path(image_path).name
        print(f"\n📊 开始处理图像: {image_name}")

        # 核心检测流程
        try:
            all_defects, light_guide_contours = self.process_image(image)
        except Exception as e:
            print(f"❌ 检测过程中发生错误 {image_name}: {str(e)}")
            return False, None

        # 生成结果图
        result_image = self.draw_results(image, all_defects, light_guide_contours)

        # 保存结果图
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True, parents=True)

        img_output_path = str(output_path / f"result_{image_name}")
        cv2.imwrite(img_output_path, result_image)

        # 仅当有缺陷时，才添加到待保存列表
        if all_defects:
            self._add_to_defective_images(image_name, all_defects, light_guide_contours)
            print(f"   - 检测到缺陷，将记录到汇总JSON")
        else:
            print(f"   - 未检测到缺陷，不记录到JSON")

        # 输出检测信息
        print(f"✅ 图像 {image_name} 处理完成")
        print(f"   - 检测到光导数量: {len(light_guide_contours)}")
        print(f"   - 总缺陷数: {len(all_defects)}")
        if all_defects:
            print("   - 缺陷详情:")
            for i, defect in enumerate(all_defects, 1):
                print(f"     {i}. {defect}")

        return True, img_output_path

    def _add_to_defective_images(self, image_name: str, defects: List[Defect], contours: List[np.ndarray]) -> None:
        """将有缺陷的图像信息添加到缓存列表"""
        # 计算光导特征
        guide_features = []
        for i, cnt in enumerate(contours, 1):
            area = cv2.contourArea(cnt)
            perimeter = cv2.arcLength(cnt, True)
            x, y, w, h = cv2.boundingRect(cnt)
            guide_features.append({
                "guide_id": i,
                "area": area,
                "perimeter": perimeter,
                "bounding_box": [x, y, w, h],
                "defect_count": sum(1 for d in defects if d.guide_id == i)
            })

        # 添加到缓存
        self.defective_images.append({
            "image_name": image_name,
            "total_guides": len(contours),
            "total_defects": len(defects),
            "guides": guide_features,
            "defects": [d.to_dict() for d in defects]
        })

    def save_combined_results(self, output_path: str) -> None:
        """保存所有有缺陷的图像结果到一个JSON文件"""
        json_path = Path(output_path) / "defects_summary.json"

        # 准备要保存的数据
        output_data = {
            "total_defective_images": len(self.defective_images),
            "total_defects": sum(img["total_defects"] for img in self.defective_images),
            "images": self.defective_images
        }

        # 保存JSON文件，不存在则创建
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"\n📄 汇总结果已保存到: {json_path.resolve()}")
        print(f"   - 包含缺陷的图像数量: {len(self.defective_images)}")
        print(f"   - 总缺陷数量: {sum(img['total_defects'] for img in self.defective_images)}")

    def process_image(self, image: np.ndarray) -> Tuple[List[Defect], List[np.ndarray]]:
        """处理单张图像的核心逻辑"""
        all_defects = []
        image_size = (image.shape[0], image.shape[1])

        # 1. 检测光导轮廓
        light_guide_contours = self.detect_light_guide_contours(image)
        if not light_guide_contours:
            print("⚠️  未检测到有效光导")
            return all_defects, light_guide_contours

        # 2. 生成光导掩码
        light_guide_masks = self.get_light_guide_masks(light_guide_contours, image_size)

        # 3. 处理每个光导
        for guide_idx, (cnt, full_mask) in enumerate(zip(light_guide_contours, light_guide_masks), 1):
            # 动态调整当前光导的检测参数（基于光导尺寸）
            guide_area = cv2.contourArea(cnt)
            adaptive_min_area = max(5, int(self.config.min_defect_area * (
                        guide_area / self.config.light_guide_min_area) ** 0.5))

            # 保存原始参数并临时修改
            original_min_area = self.config.min_defect_area
            self.config.min_defect_area = adaptive_min_area

            # 处理光导缺陷
            guide_defects = self.process_light_guide_segments(image, cnt, full_mask, guide_idx)
            all_defects.extend(guide_defects)

            # 恢复原始参数
            self.config.min_defect_area = original_min_area

        return all_defects, light_guide_contours

    def get_light_guide_masks(self, contours: List[np.ndarray], image_size: Tuple[int, int]) -> List[np.ndarray]:
        """生成光导掩码"""
        masks = []
        global_mask = self.generate_global_mask(image_size) if self.use_mask else None

        for cnt in contours:
            mask = self.generate_contour_mask(cnt, image_size)
            if self.use_mask and global_mask is not None:
                mask = cv2.bitwise_and(mask, global_mask)
            masks.append(mask)

        return masks

    def draw_results(self, image: np.ndarray, defects: List[Defect],
                     light_guide_contours: List[np.ndarray]) -> np.ndarray:
        """绘制检测结果"""
        result = image.copy()
        h, w = result.shape[:2]

        # 1. 绘制屏蔽区域
        if self.use_mask and self.mask_rects:
            for rect in self.mask_rects:
                x, y, width, height = rect
                x1 = max(0, x)
                y1 = max(0, y)
                x2 = min(w, x + width)
                y2 = min(h, y + height)
                cv2.rectangle(result, (x1, y1), (x2, y2), (0, 0, 255), 2, cv2.LINE_AA)
                cv2.putText(result, "Mask", (x1 + 5, y1 + 20),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2, cv2.LINE_AA)

        # 2. 绘制光导轮廓
        for cnt in light_guide_contours:
            cv2.drawContours(result, [cnt.astype(np.int32)], 0, (0, 255, 0), 2, cv2.LINE_AA)

        # 3. 绘制缺陷
        for i, defect in enumerate(defects):
            color = (0, 0, 255) if defect.is_bright else (255, 0, 0)
            cv2.circle(result, defect.center, defect.radius, color, 2, cv2.LINE_AA)
            cv2.putText(result, f"{i + 1}", (defect.center[0] + 5, defect.center[1] - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1, cv2.LINE_AA)

        return result

    def process_image_folder(self, input_folder: str, output_folder: str,
                             supported_ext: Tuple[str, ...] = (".jpg", ".jpeg", ".png", ".bmp")) -> None:
        """批量处理文件夹内所有图像"""
        # 1. 验证输入文件夹
        input_path = Path(input_folder)
        if not input_path.exists() or not input_path.is_dir():
            print(f"❌ 输入文件夹不存在或不是目录: {input_folder}")
            return

        # 2. 创建输出文件夹
        output_path = Path(output_folder)
        output_path.mkdir(exist_ok=True, parents=True)
        print(f"📁 结果输出目录: {output_path.resolve()}")

        # 3. 遍历所有图像文件
        image_files = [f for f in input_path.iterdir() if
                       f.is_file() and f.suffix.lower() in supported_ext]

        if not image_files:
            print(f"⚠️  输入文件夹 {input_folder} 中未找到支持的图像文件")
            return

        print(f"📋 找到 {len(image_files)} 个图像文件，开始批量处理...")

        # 4. 批量处理
        success_count = 0
        fail_count = 0
        for img_file in image_files:
            success, _ = self.process_single_image(str(img_file), str(output_path))
            if success:
                success_count += 1
            else:
                fail_count += 1

        # 5. 保存汇总结果（仅包含有缺陷的图像）
        self.save_combined_results(output_folder)

        # 6. 输出汇总报告
        print("\n" + "=" * 60)
        print("📊 批量处理完成汇总")
        print("=" * 60)
        print(f"总处理文件数: {len(image_files)}")
        print(f"处理成功: {success_count} 个")
        print(f"处理失败: {fail_count} 个")
        print(f"包含缺陷的图像: {len(self.defective_images)} 个")
        print(f"结果保存路径: {output_path.resolve()}")
        print("=" * 60)


if __name__ == "__main__":
    # -------------------------- 配置参数 --------------------------
    # 屏蔽区域（格式：(x, y, width, height)，OpenCV坐标系：x右、y下）
    MASK_RECTS = [
        (1668, 1184, 500, 500),  # 屏蔽区域1
        (9888, 1194, 900, 500)  # 屏蔽区域2
    ]
    # 输入输出文件夹配置
    INPUT_FOLDER = r"E:\LightGuideImage\LightGuide_dataset\167"  # 输入图像文件夹
    OUTPUT_FOLDER = r"167LightGuide_Detection_Results"  # 输出结果文件夹（自动创建）

    # 初始化检测配置
    cfg = DetectionConfig()
    cfg.threshold_factor = 2.0  # 对比度阈值系数
    cfg.min_defect_area = 20  # 最小缺陷面积
    cfg.min_circularity = 0.3  # 最小圆度
    cfg.segment_length = 150  # 分段长度
    cfg.light_guide_min_area = 5000  # 最小光导面积
    cfg.use_clahe = True  # 启用光照补偿

    # -------------------------- 执行批量检测 --------------------------
    # 初始化检测器
    detector = LightGuideDetector(
        config=cfg,
        use_mask=False,  # 是否启用屏蔽功能
        mask_rects=MASK_RECTS if False else None
    )

    # 执行批量处理
    detector.process_image_folder(
        input_folder=INPUT_FOLDER,
        output_folder=OUTPUT_FOLDER,
        supported_ext=(".jpg", ".jpeg", ".png")  # 支持的图像格式
    )
