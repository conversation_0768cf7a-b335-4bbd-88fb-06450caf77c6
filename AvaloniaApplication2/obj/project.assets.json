{"version": 3, "targets": {"net8.0": {"Avalonia/11.3.0": {"type": "package", "dependencies": {"Avalonia.BuildServices": "0.0.31", "Avalonia.Remote.Protocol": "11.3.0", "MicroCom.Runtime": "0.11.0"}, "compile": {"ref/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "ref/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "lib/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "build": {"buildTransitive/Avalonia.props": {}, "buildTransitive/Avalonia.targets": {}}}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x86"}}}, "Avalonia.BuildServices/0.0.31": {"type": "package", "build": {"buildTransitive/Avalonia.BuildServices.targets": {}}}, "Avalonia.Controls.ColorPicker/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Avalonia.Remote.Protocol": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}}, "Avalonia.Desktop/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Avalonia.Native": "11.3.0", "Avalonia.Skia": "11.3.0", "Avalonia.Win32": "11.3.0", "Avalonia.X11": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"related": ".xml"}}}, "Avalonia.Diagnostics/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Avalonia.Controls.ColorPicker": "11.3.0", "Avalonia.Themes.Simple": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}}, "Avalonia.Fonts.Inter/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}}, "Avalonia.FreeDesktop/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Tmds.DBus.Protocol": "0.21.2"}, "compile": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}}, "Avalonia.Native/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"assetType": "native", "rid": "osx"}}}, "Avalonia.Remote.Protocol/11.3.0": {"type": "package", "compile": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}}, "Avalonia.Skia/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "HarfBuzzSharp.NativeAssets.WebAssembly": "*******", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux": "2.88.9", "SkiaSharp.NativeAssets.WebAssembly": "2.88.9"}, "compile": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}}, "Avalonia.Themes.Fluent/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}}, "Avalonia.Themes.Simple/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}}, "Avalonia.Win32/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Avalonia.Angle.Windows.Natives": "2.1.22045.20230930"}, "compile": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Win32.dll": {"related": ".Automation.xml;.xml"}}, "runtime": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Win32.dll": {"related": ".Automation.xml;.xml"}}}, "Avalonia.X11/11.3.0": {"type": "package", "dependencies": {"Avalonia": "11.3.0", "Avalonia.FreeDesktop": "11.3.0", "Avalonia.Skia": "11.3.0"}, "compile": {"lib/net8.0/Avalonia.X11.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"related": ".xml"}}}, "CommunityToolkit.Mvvm/8.2.1": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets": {}}}, "HarfBuzzSharp/*******": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"type": "package", "dependencies": {"HarfBuzzSharp": "*******"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "IconPacks.Avalonia/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.BootstrapIcons": "1.2.0", "IconPacks.Avalonia.BoxIcons": "1.2.0", "IconPacks.Avalonia.CircumIcons": "1.2.0", "IconPacks.Avalonia.Codicons": "1.2.0", "IconPacks.Avalonia.Coolicons": "1.2.0", "IconPacks.Avalonia.Core": "1.2.0", "IconPacks.Avalonia.Entypo": "1.2.0", "IconPacks.Avalonia.EvaIcons": "1.2.0", "IconPacks.Avalonia.FeatherIcons": "1.2.0", "IconPacks.Avalonia.FileIcons": "1.2.0", "IconPacks.Avalonia.FontAwesome": "1.2.0", "IconPacks.Avalonia.FontAwesome5": "1.2.0", "IconPacks.Avalonia.FontAwesome6": "1.2.0", "IconPacks.Avalonia.Fontaudio": "1.2.0", "IconPacks.Avalonia.Fontisto": "1.2.0", "IconPacks.Avalonia.ForkAwesome": "1.2.0", "IconPacks.Avalonia.GameIcons": "1.2.0", "IconPacks.Avalonia.Ionicons": "1.2.0", "IconPacks.Avalonia.JamIcons": "1.2.0", "IconPacks.Avalonia.KeyruneIcons": "1.2.0", "IconPacks.Avalonia.Lucide": "1.2.0", "IconPacks.Avalonia.Material": "1.2.0", "IconPacks.Avalonia.MaterialDesign": "1.2.0", "IconPacks.Avalonia.MaterialLight": "1.2.0", "IconPacks.Avalonia.MemoryIcons": "1.2.0", "IconPacks.Avalonia.Microns": "1.2.0", "IconPacks.Avalonia.MingCuteIcons": "1.2.0", "IconPacks.Avalonia.Modern": "1.2.0", "IconPacks.Avalonia.MynaUIIcons": "1.2.0", "IconPacks.Avalonia.Octicons": "1.2.0", "IconPacks.Avalonia.PhosphorIcons": "1.2.0", "IconPacks.Avalonia.PicolIcons": "1.2.0", "IconPacks.Avalonia.PixelartIcons": "1.2.0", "IconPacks.Avalonia.RPGAwesome": "1.2.0", "IconPacks.Avalonia.RadixIcons": "1.2.0", "IconPacks.Avalonia.RemixIcon": "1.2.0", "IconPacks.Avalonia.SimpleIcons": "1.2.0", "IconPacks.Avalonia.Typicons": "1.2.0", "IconPacks.Avalonia.Unicons": "1.2.0", "IconPacks.Avalonia.VaadinIcons": "1.2.0", "IconPacks.Avalonia.WeatherIcons": "1.2.0", "IconPacks.Avalonia.Zondicons": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.BootstrapIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.BootstrapIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.BootstrapIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.BoxIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.BoxIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.BoxIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.CircumIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.CircumIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.CircumIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Codicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Codicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Codicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Coolicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Coolicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Coolicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Core/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Core.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Entypo/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Entypo.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Entypo.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.EvaIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.EvaIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.EvaIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.FeatherIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.FeatherIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.FeatherIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.FileIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.FileIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.FileIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Fontaudio/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Fontaudio.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Fontaudio.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.FontAwesome/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.FontAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.FontAwesome.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.FontAwesome5/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.FontAwesome5.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.FontAwesome5.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.FontAwesome6/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.FontAwesome6.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.FontAwesome6.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Fontisto/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Fontisto.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Fontisto.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.ForkAwesome/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.ForkAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.ForkAwesome.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.GameIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.GameIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.GameIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Ionicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Ionicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Ionicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.JamIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.JamIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.JamIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.KeyruneIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.KeyruneIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.KeyruneIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Lucide/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Lucide.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Lucide.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Material/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Material.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Material.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.MaterialDesign/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.MaterialDesign.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.MaterialDesign.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.MaterialLight/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.MaterialLight.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.MaterialLight.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.MemoryIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.MemoryIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.MemoryIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Microns/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Microns.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Microns.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.MingCuteIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.MingCuteIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.MingCuteIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Modern/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Modern.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Modern.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.MynaUIIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.MynaUIIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.MynaUIIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Octicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Octicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Octicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.PhosphorIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.PhosphorIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.PhosphorIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.PicolIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.PicolIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.PicolIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.PixelartIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.PixelartIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.PixelartIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.RadixIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.RadixIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.RadixIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.RemixIcon/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.RemixIcon.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.RemixIcon.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.RPGAwesome/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.RPGAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.RPGAwesome.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.SimpleIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.SimpleIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.SimpleIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Typicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Typicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Typicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Unicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Unicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Unicons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.VaadinIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.VaadinIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.VaadinIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.WeatherIcons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.WeatherIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.WeatherIcons.dll": {"related": ".xml"}}}, "IconPacks.Avalonia.Zondicons/1.2.0": {"type": "package", "dependencies": {"Avalonia": "11.0.13", "IconPacks.Avalonia.Core": "1.2.0", "System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0/IconPacks.Avalonia.Zondicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/IconPacks.Avalonia.Zondicons.dll": {"related": ".xml"}}}, "MicroCom.Runtime/0.11.0": {"type": "package", "compile": {"lib/net5.0/MicroCom.Runtime.dll": {}}, "runtime": {"lib/net5.0/MicroCom.Runtime.dll": {}}}, "OpenCvSharp4/4.8.0.20230708": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/OpenCvSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/OpenCvSharp.dll": {"related": ".xml"}}}, "OpenCvSharp4.runtime.win/4.8.0.20230708": {"type": "package", "build": {"build/net5.0/OpenCvSharp4.runtime.win.props": {}}, "runtimeTargets": {"runtimes/win-x64/native/OpenCvSharpExtern.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg480_64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/OpenCvSharpExtern.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/opencv_videoio_ffmpeg480.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"type": "package", "dependencies": {"SkiaSharp": "2.88.9"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}}}}, "libraries": {"Avalonia/11.3.0": {"sha512": "/j8J0QcekWFBl+DE/4g8WeSGwUK11auhh5d1o5pfOgi5gMrG6V9QdTsPv0ZCDL73c7dJwwL1BZn1WrhE11I76g==", "type": "package", "path": "avalonia/11.3.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Avalonia.Analyzers.dll", "analyzers/dotnet/cs/Avalonia.Generators.dll", "avalonia.11.3.0.nupkg.sha512", "avalonia.nuspec", "build/Avalonia.Generators.props", "build/Avalonia.props", "build/Avalonia.targets", "build/AvaloniaBuildTasks.props", "build/AvaloniaBuildTasks.targets", "build/AvaloniaItemSchema.xaml", "build/AvaloniaPrivateApis.targets", "build/AvaloniaRules.Project.xml", "build/AvaloniaSingleProject.targets", "build/AvaloniaVersion.props", "buildTransitive/Avalonia.Generators.props", "buildTransitive/Avalonia.props", "buildTransitive/Avalonia.targets", "buildTransitive/AvaloniaBuildTasks.props", "buildTransitive/AvaloniaBuildTasks.targets", "buildTransitive/AvaloniaItemSchema.xaml", "buildTransitive/AvaloniaPrivateApis.targets", "buildTransitive/AvaloniaRules.Project.xml", "buildTransitive/AvaloniaSingleProject.targets", "lib/net6.0/Avalonia.Base.dll", "lib/net6.0/Avalonia.Base.xml", "lib/net6.0/Avalonia.Controls.dll", "lib/net6.0/Avalonia.Controls.xml", "lib/net6.0/Avalonia.DesignerSupport.dll", "lib/net6.0/Avalonia.DesignerSupport.xml", "lib/net6.0/Avalonia.Dialogs.dll", "lib/net6.0/Avalonia.Dialogs.xml", "lib/net6.0/Avalonia.Markup.Xaml.dll", "lib/net6.0/Avalonia.Markup.Xaml.xml", "lib/net6.0/Avalonia.Markup.dll", "lib/net6.0/Avalonia.Markup.xml", "lib/net6.0/Avalonia.Metal.dll", "lib/net6.0/Avalonia.Metal.xml", "lib/net6.0/Avalonia.MicroCom.dll", "lib/net6.0/Avalonia.MicroCom.xml", "lib/net6.0/Avalonia.OpenGL.dll", "lib/net6.0/Avalonia.OpenGL.xml", "lib/net6.0/Avalonia.Vulkan.dll", "lib/net6.0/Avalonia.Vulkan.xml", "lib/net6.0/Avalonia.dll", "lib/net6.0/Avalonia.xml", "lib/net8.0/Avalonia.Base.dll", "lib/net8.0/Avalonia.Base.xml", "lib/net8.0/Avalonia.Controls.dll", "lib/net8.0/Avalonia.Controls.xml", "lib/net8.0/Avalonia.DesignerSupport.dll", "lib/net8.0/Avalonia.DesignerSupport.xml", "lib/net8.0/Avalonia.Dialogs.dll", "lib/net8.0/Avalonia.Dialogs.xml", "lib/net8.0/Avalonia.Markup.Xaml.dll", "lib/net8.0/Avalonia.Markup.Xaml.xml", "lib/net8.0/Avalonia.Markup.dll", "lib/net8.0/Avalonia.Markup.xml", "lib/net8.0/Avalonia.Metal.dll", "lib/net8.0/Avalonia.Metal.xml", "lib/net8.0/Avalonia.MicroCom.dll", "lib/net8.0/Avalonia.MicroCom.xml", "lib/net8.0/Avalonia.OpenGL.dll", "lib/net8.0/Avalonia.OpenGL.xml", "lib/net8.0/Avalonia.Vulkan.dll", "lib/net8.0/Avalonia.Vulkan.xml", "lib/net8.0/Avalonia.dll", "lib/net8.0/Avalonia.xml", "lib/netstandard2.0/Avalonia.Base.dll", "lib/netstandard2.0/Avalonia.Base.xml", "lib/netstandard2.0/Avalonia.Controls.dll", "lib/netstandard2.0/Avalonia.Controls.xml", "lib/netstandard2.0/Avalonia.DesignerSupport.dll", "lib/netstandard2.0/Avalonia.DesignerSupport.xml", "lib/netstandard2.0/Avalonia.Dialogs.dll", "lib/netstandard2.0/Avalonia.Dialogs.xml", "lib/netstandard2.0/Avalonia.Markup.Xaml.dll", "lib/netstandard2.0/Avalonia.Markup.Xaml.xml", "lib/netstandard2.0/Avalonia.Markup.dll", "lib/netstandard2.0/Avalonia.Markup.xml", "lib/netstandard2.0/Avalonia.Metal.dll", "lib/netstandard2.0/Avalonia.Metal.xml", "lib/netstandard2.0/Avalonia.MicroCom.dll", "lib/netstandard2.0/Avalonia.MicroCom.xml", "lib/netstandard2.0/Avalonia.OpenGL.dll", "lib/netstandard2.0/Avalonia.OpenGL.xml", "lib/netstandard2.0/Avalonia.Vulkan.dll", "lib/netstandard2.0/Avalonia.Vulkan.xml", "lib/netstandard2.0/Avalonia.dll", "lib/netstandard2.0/Avalonia.xml", "ref/net6.0/Avalonia.Base.dll", "ref/net6.0/Avalonia.Base.xml", "ref/net6.0/Avalonia.Controls.dll", "ref/net6.0/Avalonia.Controls.xml", "ref/net6.0/Avalonia.DesignerSupport.dll", "ref/net6.0/Avalonia.DesignerSupport.xml", "ref/net6.0/Avalonia.Dialogs.dll", "ref/net6.0/Avalonia.Dialogs.xml", "ref/net6.0/Avalonia.Markup.Xaml.dll", "ref/net6.0/Avalonia.Markup.Xaml.xml", "ref/net6.0/Avalonia.Markup.dll", "ref/net6.0/Avalonia.Markup.xml", "ref/net6.0/Avalonia.Metal.dll", "ref/net6.0/Avalonia.Metal.xml", "ref/net6.0/Avalonia.MicroCom.dll", "ref/net6.0/Avalonia.MicroCom.xml", "ref/net6.0/Avalonia.OpenGL.dll", "ref/net6.0/Avalonia.OpenGL.xml", "ref/net6.0/Avalonia.Vulkan.dll", "ref/net6.0/Avalonia.Vulkan.xml", "ref/net6.0/Avalonia.dll", "ref/net6.0/Avalonia.xml", "ref/net8.0/Avalonia.Base.dll", "ref/net8.0/Avalonia.Base.xml", "ref/net8.0/Avalonia.Controls.dll", "ref/net8.0/Avalonia.Controls.xml", "ref/net8.0/Avalonia.DesignerSupport.dll", "ref/net8.0/Avalonia.DesignerSupport.xml", "ref/net8.0/Avalonia.Dialogs.dll", "ref/net8.0/Avalonia.Dialogs.xml", "ref/net8.0/Avalonia.Markup.Xaml.dll", "ref/net8.0/Avalonia.Markup.Xaml.xml", "ref/net8.0/Avalonia.Markup.dll", "ref/net8.0/Avalonia.Markup.xml", "ref/net8.0/Avalonia.Metal.dll", "ref/net8.0/Avalonia.Metal.xml", "ref/net8.0/Avalonia.MicroCom.dll", "ref/net8.0/Avalonia.MicroCom.xml", "ref/net8.0/Avalonia.OpenGL.dll", "ref/net8.0/Avalonia.OpenGL.xml", "ref/net8.0/Avalonia.Vulkan.dll", "ref/net8.0/Avalonia.Vulkan.xml", "ref/net8.0/Avalonia.dll", "ref/net8.0/Avalonia.xml", "ref/netstandard2.0/Avalonia.Base.dll", "ref/netstandard2.0/Avalonia.Base.xml", "ref/netstandard2.0/Avalonia.Controls.dll", "ref/netstandard2.0/Avalonia.Controls.xml", "ref/netstandard2.0/Avalonia.DesignerSupport.dll", "ref/netstandard2.0/Avalonia.DesignerSupport.xml", "ref/netstandard2.0/Avalonia.Dialogs.dll", "ref/netstandard2.0/Avalonia.Dialogs.xml", "ref/netstandard2.0/Avalonia.Markup.Xaml.dll", "ref/netstandard2.0/Avalonia.Markup.Xaml.xml", "ref/netstandard2.0/Avalonia.Markup.dll", "ref/netstandard2.0/Avalonia.Markup.xml", "ref/netstandard2.0/Avalonia.Metal.dll", "ref/netstandard2.0/Avalonia.Metal.xml", "ref/netstandard2.0/Avalonia.MicroCom.dll", "ref/netstandard2.0/Avalonia.MicroCom.xml", "ref/netstandard2.0/Avalonia.OpenGL.dll", "ref/netstandard2.0/Avalonia.OpenGL.xml", "ref/netstandard2.0/Avalonia.Vulkan.dll", "ref/netstandard2.0/Avalonia.Vulkan.xml", "ref/netstandard2.0/Avalonia.dll", "ref/netstandard2.0/Avalonia.xml", "tools/net461/designer/Avalonia.Designer.HostApp.exe", "tools/netstandard2.0/Avalonia.Build.Tasks.dll", "tools/netstandard2.0/designer/Avalonia.Designer.HostApp.dll"]}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {"sha512": "Bo3qOhKC1b84BIhiogndMdAzB3UrrESKK7hS769f5HWeoMw/pcd42US5KFYW2JJ4ZSTrXnP8mXwLTMzh+S+9Lg==", "type": "package", "path": "avalonia.angle.windows.natives/2.1.22045.20230930", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE", "avalonia.angle.windows.natives.2.1.22045.20230930.nupkg.sha512", "avalonia.angle.windows.natives.nuspec", "runtimes/win-arm64/native/av_libglesv2.dll", "runtimes/win-x64/native/av_libglesv2.dll", "runtimes/win-x86/native/av_libglesv2.dll"]}, "Avalonia.BuildServices/0.0.31": {"sha512": "KmCN6Hc+45q4OnF10ge450yVUvWuxU6bdQiyKqiSvrHKpahNrEdk0kG6Ip6GHk2SKOCttGQuA206JVdkldEENg==", "type": "package", "path": "avalonia.buildservices/0.0.31", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.buildservices.0.0.31.nupkg.sha512", "avalonia.buildservices.nuspec", "build/Avalonia.BuildServices.targets", "buildTransitive/Avalonia.BuildServices.targets", "tools/netstandard2.0/Avalonia.BuildServices.Collector.dll", "tools/netstandard2.0/Avalonia.BuildServices.dll", "tools/netstandard2.0/runtimeconfig.json"]}, "Avalonia.Controls.ColorPicker/11.3.0": {"sha512": "MqwZPVdBiDc0h6DHVG5Igb+bd+3o7IOM9nbHgNPlJn38xG0jQb+YqqRcvnt/KnD5FBEaXZcGmiW3rqU9ydXvlg==", "type": "package", "path": "avalonia.controls.colorpicker/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.controls.colorpicker.11.3.0.nupkg.sha512", "avalonia.controls.colorpicker.nuspec", "lib/net6.0/Avalonia.Controls.ColorPicker.dll", "lib/net6.0/Avalonia.Controls.ColorPicker.xml", "lib/net8.0/Avalonia.Controls.ColorPicker.dll", "lib/net8.0/Avalonia.Controls.ColorPicker.xml", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.dll", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.xml"]}, "Avalonia.Desktop/11.3.0": {"sha512": "X1nUSIDHPFt34U8Dn4Hk8/c/i+rN3pA0Nv4awfYHlYdenmLqE82gm8fpKmQE6/OfguUN1yMYi/uU9lAAAbd2hg==", "type": "package", "path": "avalonia.desktop/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.desktop.11.3.0.nupkg.sha512", "avalonia.desktop.nuspec", "lib/net6.0/Avalonia.Desktop.dll", "lib/net6.0/Avalonia.Desktop.xml", "lib/net8.0/Avalonia.Desktop.dll", "lib/net8.0/Avalonia.Desktop.xml", "lib/netstandard2.0/Avalonia.Desktop.dll", "lib/netstandard2.0/Avalonia.Desktop.xml"]}, "Avalonia.Diagnostics/11.3.0": {"sha512": "P152VhCexkrvTLRiRj7mSzUSMGKwTEsbz/XWTogV/33vGVWeCC8tStf/c8YsRrU5kSITtKeCV52UOh4m46hTfA==", "type": "package", "path": "avalonia.diagnostics/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.diagnostics.11.3.0.nupkg.sha512", "avalonia.diagnostics.nuspec", "lib/net6.0/Avalonia.Diagnostics.dll", "lib/net6.0/Avalonia.Diagnostics.xml", "lib/net8.0/Avalonia.Diagnostics.dll", "lib/net8.0/Avalonia.Diagnostics.xml", "lib/netstandard2.0/Avalonia.Diagnostics.dll", "lib/netstandard2.0/Avalonia.Diagnostics.xml"]}, "Avalonia.Fonts.Inter/11.3.0": {"sha512": "1PXi6Ehk5LwZBBkgSXmxsB5iW4UVSLyS3t39it1uB5bGIhagiDMh1RK4kuiZive7f6NRDfRSDCmpsqRKNDGvLg==", "type": "package", "path": "avalonia.fonts.inter/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.fonts.inter.11.3.0.nupkg.sha512", "avalonia.fonts.inter.nuspec", "lib/net6.0/Avalonia.Fonts.Inter.dll", "lib/net6.0/Avalonia.Fonts.Inter.xml", "lib/net8.0/Avalonia.Fonts.Inter.dll", "lib/net8.0/Avalonia.Fonts.Inter.xml", "lib/netstandard2.0/Avalonia.Fonts.Inter.dll", "lib/netstandard2.0/Avalonia.Fonts.Inter.xml"]}, "Avalonia.FreeDesktop/11.3.0": {"sha512": "NUoXEEmZ4gbXn5c2Smv2QfDT23Ps2x2O1Dls5/op1AjmxZFXRkx/zz1xQVnJu6cHEA2Jx4i+SG7AvCPVs8bBBA==", "type": "package", "path": "avalonia.freedesktop/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.freedesktop.11.3.0.nupkg.sha512", "avalonia.freedesktop.nuspec", "lib/net6.0/Avalonia.FreeDesktop.dll", "lib/net6.0/Avalonia.FreeDesktop.xml", "lib/net8.0/Avalonia.FreeDesktop.dll", "lib/net8.0/Avalonia.FreeDesktop.xml", "lib/netstandard2.0/Avalonia.FreeDesktop.dll", "lib/netstandard2.0/Avalonia.FreeDesktop.xml"]}, "Avalonia.Native/11.3.0": {"sha512": "1KFRYgxY9IVxceXB+/jRH0zCrhcKDOs/mTxLexZGd5MT8oKs0NTnAHP8R09bCQOw9aGcYLpzXVzKzhpGulxxWQ==", "type": "package", "path": "avalonia.native/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.native.11.3.0.nupkg.sha512", "avalonia.native.nuspec", "lib/net6.0/Avalonia.Native.dll", "lib/net6.0/Avalonia.Native.xml", "lib/net8.0/Avalonia.Native.dll", "lib/net8.0/Avalonia.Native.xml", "lib/netstandard2.0/Avalonia.Native.dll", "lib/netstandard2.0/Avalonia.Native.xml", "runtimes/osx/native/libAvaloniaNative.dylib"]}, "Avalonia.Remote.Protocol/11.3.0": {"sha512": "noH0+YgFsXshhDnEcBATw5EULNiTYv4gznE2OnSPDvU5K9/COVs6W3ZWlYY1lwY+w4x1IGueMux7dcw0TbjEzA==", "type": "package", "path": "avalonia.remote.protocol/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.remote.protocol.11.3.0.nupkg.sha512", "avalonia.remote.protocol.nuspec", "lib/net6.0/Avalonia.Remote.Protocol.dll", "lib/net6.0/Avalonia.Remote.Protocol.xml", "lib/net8.0/Avalonia.Remote.Protocol.dll", "lib/net8.0/Avalonia.Remote.Protocol.xml", "lib/netstandard2.0/Avalonia.Remote.Protocol.dll", "lib/netstandard2.0/Avalonia.Remote.Protocol.xml"]}, "Avalonia.Skia/11.3.0": {"sha512": "6R3+Qp3u0W70MFAxBD4bjRVwQtXs7TstsD2eqLR1HOJI07sha2dh2GoogOI5hKLBn32fD6gek7iSJL6kqH5OiA==", "type": "package", "path": "avalonia.skia/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.skia.11.3.0.nupkg.sha512", "avalonia.skia.nuspec", "lib/net6.0/Avalonia.Skia.dll", "lib/net6.0/Avalonia.Skia.xml", "lib/net8.0/Avalonia.Skia.dll", "lib/net8.0/Avalonia.Skia.xml", "lib/netstandard2.0/Avalonia.Skia.dll", "lib/netstandard2.0/Avalonia.Skia.xml"]}, "Avalonia.Themes.Fluent/11.3.0": {"sha512": "tRcR0xKhpnHikIhtiE1vchuNHD/hvqLtto9yKmw73eZyhsyQMCncCu3GGfWTCCHOxJfawdMUCfUWo8COqmgLZw==", "type": "package", "path": "avalonia.themes.fluent/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.fluent.11.3.0.nupkg.sha512", "avalonia.themes.fluent.nuspec", "lib/net6.0/Avalonia.Themes.Fluent.dll", "lib/net6.0/Avalonia.Themes.Fluent.xml", "lib/net8.0/Avalonia.Themes.Fluent.dll", "lib/net8.0/Avalonia.Themes.Fluent.xml", "lib/netstandard2.0/Avalonia.Themes.Fluent.dll", "lib/netstandard2.0/Avalonia.Themes.Fluent.xml"]}, "Avalonia.Themes.Simple/11.3.0": {"sha512": "pGurEpish9NS89iiBLbvIlN+DJ5hGaW1ABxcIl6XL9M/WcvUPtLznxoI0dbW2zw8OLDSsvowbsvQLdyknEvmhA==", "type": "package", "path": "avalonia.themes.simple/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.simple.11.3.0.nupkg.sha512", "avalonia.themes.simple.nuspec", "lib/net6.0/Avalonia.Themes.Simple.dll", "lib/net6.0/Avalonia.Themes.Simple.xml", "lib/net8.0/Avalonia.Themes.Simple.dll", "lib/net8.0/Avalonia.Themes.Simple.xml", "lib/netstandard2.0/Avalonia.Themes.Simple.dll", "lib/netstandard2.0/Avalonia.Themes.Simple.xml"]}, "Avalonia.Win32/11.3.0": {"sha512": "25gOjtAyWTEMCfOGioJEZ1iOH2jdOTnVpyK4y8ku1n4CKwnsml9XFbQum253y5rFlty29Vs0TiYzsC0CWuWAVg==", "type": "package", "path": "avalonia.win32/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.win32.11.3.0.nupkg.sha512", "avalonia.win32.nuspec", "lib/net6.0/Avalonia.Win32.Automation.dll", "lib/net6.0/Avalonia.Win32.Automation.xml", "lib/net6.0/Avalonia.Win32.dll", "lib/net6.0/Avalonia.Win32.xml", "lib/net8.0/Avalonia.Win32.Automation.dll", "lib/net8.0/Avalonia.Win32.Automation.xml", "lib/net8.0/Avalonia.Win32.dll", "lib/net8.0/Avalonia.Win32.xml", "lib/netstandard2.0/Avalonia.Win32.Automation.dll", "lib/netstandard2.0/Avalonia.Win32.Automation.xml", "lib/netstandard2.0/Avalonia.Win32.dll", "lib/netstandard2.0/Avalonia.Win32.xml"]}, "Avalonia.X11/11.3.0": {"sha512": "BFkNmxW0pyqrg7q62tAEv7ZCfkR4k2bz39B3umU2VLRrYpsH/b2DocXeqgeYZYJbHikSPAXLkY34IHLZtdkU1w==", "type": "package", "path": "avalonia.x11/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.x11.11.3.0.nupkg.sha512", "avalonia.x11.nuspec", "lib/net6.0/Avalonia.X11.dll", "lib/net6.0/Avalonia.X11.xml", "lib/net8.0/Avalonia.X11.dll", "lib/net8.0/Avalonia.X11.xml", "lib/netstandard2.0/Avalonia.X11.dll", "lib/netstandard2.0/Avalonia.X11.xml"]}, "CommunityToolkit.Mvvm/8.2.1": {"sha512": "I24ofWVEdplxYjUez9/bljv/qb8r8Ccj6cvYXHexNBegLaD3iDy3QrzAAOYVMmfGWIXxlU1ZtECQNfU07+6hXQ==", "type": "package", "path": "communitytoolkit.mvvm/8.2.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/netstandard2.0/CommunityToolkit.Mvvm.targets", "build/netstandard2.1/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.0/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.2.1.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "HarfBuzzSharp/*******": {"sha512": "Hq+5+gx10coOvuRgB13KBwiWxJq1QeYuhtVLbA01ZCWaugOnolUahF44KvrQTUUHDNk/C7HB6SMaebsZeOdhgg==", "type": "package", "path": "harfbuzzsharp/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.*******.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"sha512": "hkcHeTfOyIeJuPtO/QfoqkDvV/MXebZYaA/Bn/S+nXsjH3Wt9oQ6okH2kklYO+1UUdBSJFd67bi9IrpQXI2mPw==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"sha512": "UAwIYnkbBTzBJv1Id8FijY/i8QiIepRemSXufU8fyzwWhYJdx4+ajG8yQUie5HW/uusbVLFSr26muSlJOFDgSw==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {"sha512": "OpheDNp9a3nC6hWNACemWkNEXJ4tWP3Gw9bykw3FbyeEmU2nUDtLIp6VgNnjHAPRMgUs1Kl7m4gJpzVYwC7CZw==", "type": "package", "path": "harfbuzzsharp.nativeassets.webassembly/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.23/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.6/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt,simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/simd,mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/simd,st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.7/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "harfbuzzsharp.nativeassets.webassembly.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.webassembly.nuspec", "lib/netstandard1.0/_._"]}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"sha512": "RPxRXD16KtSs8Yxr2RK9Qs7AwyN9MlpqZIYs0AvfaJwl7RAtVhC0+u2f2SKwX0uMYYd3O98Z+OBA1sj6aWVKQA==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "IconPacks.Avalonia/1.2.0": {"sha512": "nwuBx+aLvSDQfLPCtsqsMyYSSC0hiGRq5+CjzTX/oJ6RndSzDXGYYpk/EBPj2ve3ZeVy9bCQaAzZ1GpxSIZT8A==", "type": "package", "path": "iconpacks.avalonia/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.1.2.0.nupkg.sha512", "iconpacks.avalonia.nuspec", "lib/net6.0/IconPacks.Avalonia.dll", "lib/net6.0/IconPacks.Avalonia.xml", "lib/net8.0/IconPacks.Avalonia.dll", "lib/net8.0/IconPacks.Avalonia.xml", "lib/netstandard2.0/IconPacks.Avalonia.dll", "lib/netstandard2.0/IconPacks.Avalonia.xml", "logo_small.png"]}, "IconPacks.Avalonia.BootstrapIcons/1.2.0": {"sha512": "+iLlES+jpy86/aDG3ZQKSaXiNYi2mwsZkj2Hf0H657hddRLH6S+nq8k9trx7ihuGucCrgZHqqu0VnAN2atF+sw==", "type": "package", "path": "iconpacks.avalonia.bootstrapicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.bootstrapicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.bootstrapicons.nuspec", "lib/net6.0/IconPacks.Avalonia.BootstrapIcons.dll", "lib/net6.0/IconPacks.Avalonia.BootstrapIcons.xml", "lib/net8.0/IconPacks.Avalonia.BootstrapIcons.dll", "lib/net8.0/IconPacks.Avalonia.BootstrapIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.BootstrapIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.BootstrapIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.BoxIcons/1.2.0": {"sha512": "f/b00dXJHk+MuyluNN0RqB3cIEcPFL+1BhSfpCf7CDpG0cTnNMhMbVAcL/EjuaCBG1z5DFbwcU8YgP2OgE1m9g==", "type": "package", "path": "iconpacks.avalonia.boxicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.boxicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.boxicons.nuspec", "lib/net6.0/IconPacks.Avalonia.BoxIcons.dll", "lib/net6.0/IconPacks.Avalonia.BoxIcons.xml", "lib/net8.0/IconPacks.Avalonia.BoxIcons.dll", "lib/net8.0/IconPacks.Avalonia.BoxIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.BoxIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.BoxIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.CircumIcons/1.2.0": {"sha512": "nZMchPwpvKDZiXLGzaMCwbHfk4pdrqhPwxnIHmQzoapyNuw+LGdvscw43igIPFNhSNfpy0JWj9bADx5HJ//eLw==", "type": "package", "path": "iconpacks.avalonia.circumicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.circumicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.circumicons.nuspec", "lib/net6.0/IconPacks.Avalonia.CircumIcons.dll", "lib/net6.0/IconPacks.Avalonia.CircumIcons.xml", "lib/net8.0/IconPacks.Avalonia.CircumIcons.dll", "lib/net8.0/IconPacks.Avalonia.CircumIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.CircumIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.CircumIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Codicons/1.2.0": {"sha512": "CRcb5/qGOQE4d/gRAzz6Kqaey3kLrVoembN10OFEC1xh7INejDVEsQkqdg1tG8Cj0Exc8wTj66Unvbv1W0h9dg==", "type": "package", "path": "iconpacks.avalonia.codicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.codicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.codicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Codicons.dll", "lib/net6.0/IconPacks.Avalonia.Codicons.xml", "lib/net8.0/IconPacks.Avalonia.Codicons.dll", "lib/net8.0/IconPacks.Avalonia.Codicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Codicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Codicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Coolicons/1.2.0": {"sha512": "ikXGnNoEBQVINJBNqYrD1y5e2gwQa9X8X+BWzmTLqIMz+r11P6MatnTeRcrk0yZxeylM3+46s7Gh2EbRZFM+lw==", "type": "package", "path": "iconpacks.avalonia.coolicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.coolicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.coolicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Coolicons.dll", "lib/net6.0/IconPacks.Avalonia.Coolicons.xml", "lib/net8.0/IconPacks.Avalonia.Coolicons.dll", "lib/net8.0/IconPacks.Avalonia.Coolicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Coolicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Coolicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Core/1.2.0": {"sha512": "l3Jp67c9U8SwHeqyEF6Y6SAIeMGpvftmYZzw/pvYpgf16N+QyNIYunHi7IbMXWz2bnJwJI25Extg86g3r8koHw==", "type": "package", "path": "iconpacks.avalonia.core/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.core.1.2.0.nupkg.sha512", "iconpacks.avalonia.core.nuspec", "lib/net6.0/IconPacks.Avalonia.Core.dll", "lib/net6.0/IconPacks.Avalonia.Core.xml", "lib/net8.0/IconPacks.Avalonia.Core.dll", "lib/net8.0/IconPacks.Avalonia.Core.xml", "lib/netstandard2.0/IconPacks.Avalonia.Core.dll", "lib/netstandard2.0/IconPacks.Avalonia.Core.xml", "logo_small.png"]}, "IconPacks.Avalonia.Entypo/1.2.0": {"sha512": "wXYfTrJzlgta6LPm0p9r8//Uuz3dyL1cxKyy8+YSNllw2ZZLPNfsuAKEj8vNQpPFdEPJksEUYiTLvvcwIuoiJQ==", "type": "package", "path": "iconpacks.avalonia.entypo/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.entypo.1.2.0.nupkg.sha512", "iconpacks.avalonia.entypo.nuspec", "lib/net6.0/IconPacks.Avalonia.Entypo.dll", "lib/net6.0/IconPacks.Avalonia.Entypo.xml", "lib/net8.0/IconPacks.Avalonia.Entypo.dll", "lib/net8.0/IconPacks.Avalonia.Entypo.xml", "lib/netstandard2.0/IconPacks.Avalonia.Entypo.dll", "lib/netstandard2.0/IconPacks.Avalonia.Entypo.xml", "logo_small.png"]}, "IconPacks.Avalonia.EvaIcons/1.2.0": {"sha512": "j6ZosOaAgXq5Ba96XpOjNTI1iILOwmGTNQp+kaNGDn7n+N2lIXHzCBSJZkhxIeKqzlK/wyeSHA9tyglJHdPA+w==", "type": "package", "path": "iconpacks.avalonia.evaicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.evaicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.evaicons.nuspec", "lib/net6.0/IconPacks.Avalonia.EvaIcons.dll", "lib/net6.0/IconPacks.Avalonia.EvaIcons.xml", "lib/net8.0/IconPacks.Avalonia.EvaIcons.dll", "lib/net8.0/IconPacks.Avalonia.EvaIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.EvaIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.EvaIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.FeatherIcons/1.2.0": {"sha512": "PnHqENJNfurdlpX5duRWNYvHkVqRDvQ3RDbrq8jStzo7HLRmpMTUc2fOer3l1XvTgwCSHBimcu/u1KzA4tonmg==", "type": "package", "path": "iconpacks.avalonia.feathericons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.feathericons.1.2.0.nupkg.sha512", "iconpacks.avalonia.feathericons.nuspec", "lib/net6.0/IconPacks.Avalonia.FeatherIcons.dll", "lib/net6.0/IconPacks.Avalonia.FeatherIcons.xml", "lib/net8.0/IconPacks.Avalonia.FeatherIcons.dll", "lib/net8.0/IconPacks.Avalonia.FeatherIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.FeatherIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.FeatherIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.FileIcons/1.2.0": {"sha512": "Dv6csune7Ugr2dS6oQ+jyPYaZwf2rQ+nLryBZJvmQw+3RGT5C512baDQCOXkFV/Y6ThSyI8K6fwC77Ky3YAsGg==", "type": "package", "path": "iconpacks.avalonia.fileicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fileicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.fileicons.nuspec", "lib/net6.0/IconPacks.Avalonia.FileIcons.dll", "lib/net6.0/IconPacks.Avalonia.FileIcons.xml", "lib/net8.0/IconPacks.Avalonia.FileIcons.dll", "lib/net8.0/IconPacks.Avalonia.FileIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.FileIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.FileIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Fontaudio/1.2.0": {"sha512": "DmHrDOR1PepCEfgf11/dZG3J0GoPOjZo9SD9SO0CQPSqqjggevQVpNwCnVPchKw3mju5eFdiOLJPjRq+Skpw3A==", "type": "package", "path": "iconpacks.avalonia.fontaudio/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fontaudio.1.2.0.nupkg.sha512", "iconpacks.avalonia.fontaudio.nuspec", "lib/net6.0/IconPacks.Avalonia.Fontaudio.dll", "lib/net6.0/IconPacks.Avalonia.Fontaudio.xml", "lib/net8.0/IconPacks.Avalonia.Fontaudio.dll", "lib/net8.0/IconPacks.Avalonia.Fontaudio.xml", "lib/netstandard2.0/IconPacks.Avalonia.Fontaudio.dll", "lib/netstandard2.0/IconPacks.Avalonia.Fontaudio.xml", "logo_small.png"]}, "IconPacks.Avalonia.FontAwesome/1.2.0": {"sha512": "ss43cpuAuw9XhWKH8ZaRqNBl85V9+22o3z4Nl/mV4RMMDY6IDkk1K80IISnQvSgt9/zA/dHIelcZqolHcIwDBQ==", "type": "package", "path": "iconpacks.avalonia.fontawesome/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fontawesome.1.2.0.nupkg.sha512", "iconpacks.avalonia.fontawesome.nuspec", "lib/net6.0/IconPacks.Avalonia.FontAwesome.dll", "lib/net6.0/IconPacks.Avalonia.FontAwesome.xml", "lib/net8.0/IconPacks.Avalonia.FontAwesome.dll", "lib/net8.0/IconPacks.Avalonia.FontAwesome.xml", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome.dll", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome.xml", "logo_small.png"]}, "IconPacks.Avalonia.FontAwesome5/1.2.0": {"sha512": "fiB8/9RYgYpmGnizsskNobVIKI33xeO3tVABn+MrH33o/81+eYWXI5k8/ncfEn2U62mo1o3qBzLTb3o9uPdrXw==", "type": "package", "path": "iconpacks.avalonia.fontawesome5/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fontawesome5.1.2.0.nupkg.sha512", "iconpacks.avalonia.fontawesome5.nuspec", "lib/net6.0/IconPacks.Avalonia.FontAwesome5.dll", "lib/net6.0/IconPacks.Avalonia.FontAwesome5.xml", "lib/net8.0/IconPacks.Avalonia.FontAwesome5.dll", "lib/net8.0/IconPacks.Avalonia.FontAwesome5.xml", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome5.dll", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome5.xml", "logo_small.png"]}, "IconPacks.Avalonia.FontAwesome6/1.2.0": {"sha512": "z/fwuuDqHaGBWRKbFeWvWSRXYrOV8TZ5SHfpfTsHwIpBM9rG/wXMVPiXsXQD7u6aFeG/D6upPLxsZJWVsdypNQ==", "type": "package", "path": "iconpacks.avalonia.fontawesome6/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fontawesome6.1.2.0.nupkg.sha512", "iconpacks.avalonia.fontawesome6.nuspec", "lib/net6.0/IconPacks.Avalonia.FontAwesome6.dll", "lib/net6.0/IconPacks.Avalonia.FontAwesome6.xml", "lib/net8.0/IconPacks.Avalonia.FontAwesome6.dll", "lib/net8.0/IconPacks.Avalonia.FontAwesome6.xml", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome6.dll", "lib/netstandard2.0/IconPacks.Avalonia.FontAwesome6.xml", "logo_small.png"]}, "IconPacks.Avalonia.Fontisto/1.2.0": {"sha512": "zcThL2jIEmX6esDlXWd5zHZ1QNX6T7vXbzjhlplQ7pGSZl+5/PVNalemD/6d0FanQmcz/ZOqPOh1gHoSC9kkkQ==", "type": "package", "path": "iconpacks.avalonia.fontisto/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.fontisto.1.2.0.nupkg.sha512", "iconpacks.avalonia.fontisto.nuspec", "lib/net6.0/IconPacks.Avalonia.Fontisto.dll", "lib/net6.0/IconPacks.Avalonia.Fontisto.xml", "lib/net8.0/IconPacks.Avalonia.Fontisto.dll", "lib/net8.0/IconPacks.Avalonia.Fontisto.xml", "lib/netstandard2.0/IconPacks.Avalonia.Fontisto.dll", "lib/netstandard2.0/IconPacks.Avalonia.Fontisto.xml", "logo_small.png"]}, "IconPacks.Avalonia.ForkAwesome/1.2.0": {"sha512": "0535HFWGPkJA5kFaz/Xx9o/VBKm2dZp2GtvFeqhIzJtOhK9V0pYUaEsVsO1xUNPPWrjfR+dOdUg1gA+TQCXRIg==", "type": "package", "path": "iconpacks.avalonia.forkawesome/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.forkawesome.1.2.0.nupkg.sha512", "iconpacks.avalonia.forkawesome.nuspec", "lib/net6.0/IconPacks.Avalonia.ForkAwesome.dll", "lib/net6.0/IconPacks.Avalonia.ForkAwesome.xml", "lib/net8.0/IconPacks.Avalonia.ForkAwesome.dll", "lib/net8.0/IconPacks.Avalonia.ForkAwesome.xml", "lib/netstandard2.0/IconPacks.Avalonia.ForkAwesome.dll", "lib/netstandard2.0/IconPacks.Avalonia.ForkAwesome.xml", "logo_small.png"]}, "IconPacks.Avalonia.GameIcons/1.2.0": {"sha512": "yIYAIIMdoo9BXClfn8qOROuL7Kw2ZyrZpMNm5Qzm8lr+haHpc/qfa56jqiCqw11w/HK1HH2bHGqjNisWDoXxWQ==", "type": "package", "path": "iconpacks.avalonia.gameicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.gameicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.gameicons.nuspec", "lib/net6.0/IconPacks.Avalonia.GameIcons.dll", "lib/net6.0/IconPacks.Avalonia.GameIcons.xml", "lib/net8.0/IconPacks.Avalonia.GameIcons.dll", "lib/net8.0/IconPacks.Avalonia.GameIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.GameIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.GameIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Ionicons/1.2.0": {"sha512": "Kk4gdY3oIlux/CrgxgyMsoCJsy+MuKCQ/qBBz5m3Z03ctMNAzs29TceT+/PbGYm6c7DJfQFK3tqI/1id+oa2Dw==", "type": "package", "path": "iconpacks.avalonia.ionicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.ionicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.ionicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Ionicons.dll", "lib/net6.0/IconPacks.Avalonia.Ionicons.xml", "lib/net8.0/IconPacks.Avalonia.Ionicons.dll", "lib/net8.0/IconPacks.Avalonia.Ionicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Ionicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Ionicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.JamIcons/1.2.0": {"sha512": "Ehd2NmH/mi1LRbtuQ9F+VcunfQX/ETyuTReTj9ogcXwQRVgfinkthX6r46uUqR7eSyGd0j2SQf+S1HWuoGu4rA==", "type": "package", "path": "iconpacks.avalonia.jamicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.jamicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.jamicons.nuspec", "lib/net6.0/IconPacks.Avalonia.JamIcons.dll", "lib/net6.0/IconPacks.Avalonia.JamIcons.xml", "lib/net8.0/IconPacks.Avalonia.JamIcons.dll", "lib/net8.0/IconPacks.Avalonia.JamIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.JamIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.JamIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.KeyruneIcons/1.2.0": {"sha512": "zPdKlqVpb/YuXz7Q09D3QweuJy28O0Uq7WQx1F9fmQV4zDNb/lYSLrHJfhnRzzFf8Ho5RO1+EYEIghxmL5Wrnw==", "type": "package", "path": "iconpacks.avalonia.keyruneicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.keyruneicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.keyruneicons.nuspec", "lib/net6.0/IconPacks.Avalonia.KeyruneIcons.dll", "lib/net6.0/IconPacks.Avalonia.KeyruneIcons.xml", "lib/net8.0/IconPacks.Avalonia.KeyruneIcons.dll", "lib/net8.0/IconPacks.Avalonia.KeyruneIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.KeyruneIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.KeyruneIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Lucide/1.2.0": {"sha512": "Xww85vZn2IEQ0+eOmWOWsz6cFVjVKGZecpHV/ZeOmAE5OY1b0nBm99jZp7mSIww2TYB3DqbmPzpYPGJrRGte3g==", "type": "package", "path": "iconpacks.avalonia.lucide/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.lucide.1.2.0.nupkg.sha512", "iconpacks.avalonia.lucide.nuspec", "lib/net6.0/IconPacks.Avalonia.Lucide.dll", "lib/net6.0/IconPacks.Avalonia.Lucide.xml", "lib/net8.0/IconPacks.Avalonia.Lucide.dll", "lib/net8.0/IconPacks.Avalonia.Lucide.xml", "lib/netstandard2.0/IconPacks.Avalonia.Lucide.dll", "lib/netstandard2.0/IconPacks.Avalonia.Lucide.xml", "logo_small.png"]}, "IconPacks.Avalonia.Material/1.2.0": {"sha512": "lgLmgJRt6YhF34BmVrF7Ekrz+GBaq83rea3s6FVQBKIOUCeGadRDrYpGny9Lug8CbnyJtlVhvWkOPLHefFaNSg==", "type": "package", "path": "iconpacks.avalonia.material/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.material.1.2.0.nupkg.sha512", "iconpacks.avalonia.material.nuspec", "lib/net6.0/IconPacks.Avalonia.Material.dll", "lib/net6.0/IconPacks.Avalonia.Material.xml", "lib/net8.0/IconPacks.Avalonia.Material.dll", "lib/net8.0/IconPacks.Avalonia.Material.xml", "lib/netstandard2.0/IconPacks.Avalonia.Material.dll", "lib/netstandard2.0/IconPacks.Avalonia.Material.xml", "logo_small.png"]}, "IconPacks.Avalonia.MaterialDesign/1.2.0": {"sha512": "2yX1O0MieRY/RUavItsULa1ShWx2C3G/NpU2BUbRqsdvC0KByLS2MCSLEB7A9Ov0LP03YL41U0Pwl35TtjRUFA==", "type": "package", "path": "iconpacks.avalonia.materialdesign/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.materialdesign.1.2.0.nupkg.sha512", "iconpacks.avalonia.materialdesign.nuspec", "lib/net6.0/IconPacks.Avalonia.MaterialDesign.dll", "lib/net6.0/IconPacks.Avalonia.MaterialDesign.xml", "lib/net8.0/IconPacks.Avalonia.MaterialDesign.dll", "lib/net8.0/IconPacks.Avalonia.MaterialDesign.xml", "lib/netstandard2.0/IconPacks.Avalonia.MaterialDesign.dll", "lib/netstandard2.0/IconPacks.Avalonia.MaterialDesign.xml", "logo_small.png"]}, "IconPacks.Avalonia.MaterialLight/1.2.0": {"sha512": "VAlEOy2RoaildG+TfQvQGYTSkZHR8Ru75H3pd/4DdnJreyFFAiK2FBbGdh0V6wZBT4mJFBPoOREUdIqu1SM+AQ==", "type": "package", "path": "iconpacks.avalonia.materiallight/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.materiallight.1.2.0.nupkg.sha512", "iconpacks.avalonia.materiallight.nuspec", "lib/net6.0/IconPacks.Avalonia.MaterialLight.dll", "lib/net6.0/IconPacks.Avalonia.MaterialLight.xml", "lib/net8.0/IconPacks.Avalonia.MaterialLight.dll", "lib/net8.0/IconPacks.Avalonia.MaterialLight.xml", "lib/netstandard2.0/IconPacks.Avalonia.MaterialLight.dll", "lib/netstandard2.0/IconPacks.Avalonia.MaterialLight.xml", "logo_small.png"]}, "IconPacks.Avalonia.MemoryIcons/1.2.0": {"sha512": "33ksQKfuAkL9Zwc3/sz5yTqUWEW1u0d4NL2y7SdPukCzPEJxzbpljfASJG4aQ0/7K9GBw4q95qGlDtKr9XlvnQ==", "type": "package", "path": "iconpacks.avalonia.memoryicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.memoryicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.memoryicons.nuspec", "lib/net6.0/IconPacks.Avalonia.MemoryIcons.dll", "lib/net6.0/IconPacks.Avalonia.MemoryIcons.xml", "lib/net8.0/IconPacks.Avalonia.MemoryIcons.dll", "lib/net8.0/IconPacks.Avalonia.MemoryIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.MemoryIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.MemoryIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Microns/1.2.0": {"sha512": "EjBUatfc553nYB8WoHC/yJ3PXvZ6TB3HQb4d7R9pZ679zR2UwqEWkewBKFq87F7prKFiBvZC8soIOENoVtEu/g==", "type": "package", "path": "iconpacks.avalonia.microns/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.microns.1.2.0.nupkg.sha512", "iconpacks.avalonia.microns.nuspec", "lib/net6.0/IconPacks.Avalonia.Microns.dll", "lib/net6.0/IconPacks.Avalonia.Microns.xml", "lib/net8.0/IconPacks.Avalonia.Microns.dll", "lib/net8.0/IconPacks.Avalonia.Microns.xml", "lib/netstandard2.0/IconPacks.Avalonia.Microns.dll", "lib/netstandard2.0/IconPacks.Avalonia.Microns.xml", "logo_small.png"]}, "IconPacks.Avalonia.MingCuteIcons/1.2.0": {"sha512": "267RYZSDAPg2CjjGyrrMf6gF8zeSeK3U8McGPDDb0pn+syiB/RM9aSAHdoL9l1znp/lJ7JrlDPIVJWCkDvdFOw==", "type": "package", "path": "iconpacks.avalonia.mingcuteicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.mingcuteicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.mingcuteicons.nuspec", "lib/net6.0/IconPacks.Avalonia.MingCuteIcons.dll", "lib/net6.0/IconPacks.Avalonia.MingCuteIcons.xml", "lib/net8.0/IconPacks.Avalonia.MingCuteIcons.dll", "lib/net8.0/IconPacks.Avalonia.MingCuteIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.MingCuteIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.MingCuteIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Modern/1.2.0": {"sha512": "nrBxcTY4L7mGmhJMtll0oUCKag/Ht4WlQICT6oVnwvjO+ZtWtOEUH8iZ0tOYPFxnITO0DbzlgOCbXEsOHgWJew==", "type": "package", "path": "iconpacks.avalonia.modern/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.modern.1.2.0.nupkg.sha512", "iconpacks.avalonia.modern.nuspec", "lib/net6.0/IconPacks.Avalonia.Modern.dll", "lib/net6.0/IconPacks.Avalonia.Modern.xml", "lib/net8.0/IconPacks.Avalonia.Modern.dll", "lib/net8.0/IconPacks.Avalonia.Modern.xml", "lib/netstandard2.0/IconPacks.Avalonia.Modern.dll", "lib/netstandard2.0/IconPacks.Avalonia.Modern.xml", "logo_small.png"]}, "IconPacks.Avalonia.MynaUIIcons/1.2.0": {"sha512": "CQroPVsdXRKFCKxpX4GoIQbV+MLCyTqBAmArHxnr2LcsRO4te0xuZHiaBR0PCMqGR39Dv2J7kVMKEEFbJxSgAQ==", "type": "package", "path": "iconpacks.avalonia.mynauiicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.mynauiicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.mynauiicons.nuspec", "lib/net6.0/IconPacks.Avalonia.MynaUIIcons.dll", "lib/net6.0/IconPacks.Avalonia.MynaUIIcons.xml", "lib/net8.0/IconPacks.Avalonia.MynaUIIcons.dll", "lib/net8.0/IconPacks.Avalonia.MynaUIIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.MynaUIIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.MynaUIIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Octicons/1.2.0": {"sha512": "HQqbCpwyRuOwtnu2hs7aufH0oEs4eNuwh3/D9CjlyLu5Xtt4ebZkCmflHBX9Oz1eN/8c6Wiyr4pfdtrx9DFKBQ==", "type": "package", "path": "iconpacks.avalonia.octicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.octicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.octicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Octicons.dll", "lib/net6.0/IconPacks.Avalonia.Octicons.xml", "lib/net8.0/IconPacks.Avalonia.Octicons.dll", "lib/net8.0/IconPacks.Avalonia.Octicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Octicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Octicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.PhosphorIcons/1.2.0": {"sha512": "nteHZuMwDJwpXWawNotyCxxlGvgtiXxCvOCSj5mR5v/clZpRKnMZAedY1ojDMEyxWDlirZmAv0L90b4vazyryw==", "type": "package", "path": "iconpacks.avalonia.phosphoricons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.phosphoricons.1.2.0.nupkg.sha512", "iconpacks.avalonia.phosphoricons.nuspec", "lib/net6.0/IconPacks.Avalonia.PhosphorIcons.dll", "lib/net6.0/IconPacks.Avalonia.PhosphorIcons.xml", "lib/net8.0/IconPacks.Avalonia.PhosphorIcons.dll", "lib/net8.0/IconPacks.Avalonia.PhosphorIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.PhosphorIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.PhosphorIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.PicolIcons/1.2.0": {"sha512": "8U+a2yEXQu3hflvF2ivf4pd3d62EB+wLPZKn31wyuhjUtr/mp7u5lGI6/5Ja6Nw7aevp/5Imj1oVl6CFiOz+hA==", "type": "package", "path": "iconpacks.avalonia.picolicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.picolicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.picolicons.nuspec", "lib/net6.0/IconPacks.Avalonia.PicolIcons.dll", "lib/net6.0/IconPacks.Avalonia.PicolIcons.xml", "lib/net8.0/IconPacks.Avalonia.PicolIcons.dll", "lib/net8.0/IconPacks.Avalonia.PicolIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.PicolIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.PicolIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.PixelartIcons/1.2.0": {"sha512": "w84UicrMTwH6LxVebju+eeXDRxdxx69asDfdEzk5VpqH7Qk5Yo/BLTLi3teiP3JCzznTFCwzuL/KeteXusvvKA==", "type": "package", "path": "iconpacks.avalonia.pixelarticons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.pixelarticons.1.2.0.nupkg.sha512", "iconpacks.avalonia.pixelarticons.nuspec", "lib/net6.0/IconPacks.Avalonia.PixelartIcons.dll", "lib/net6.0/IconPacks.Avalonia.PixelartIcons.xml", "lib/net8.0/IconPacks.Avalonia.PixelartIcons.dll", "lib/net8.0/IconPacks.Avalonia.PixelartIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.PixelartIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.PixelartIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.RadixIcons/1.2.0": {"sha512": "nkedyJ4ac3ZsInC4Y6h7KCRgE6X83facoXddboqbCV2pglMlCHpqOpBbgEt4/xkPtcxolvpOjVg0B8QNYbW2dQ==", "type": "package", "path": "iconpacks.avalonia.radixicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.radixicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.radixicons.nuspec", "lib/net6.0/IconPacks.Avalonia.RadixIcons.dll", "lib/net6.0/IconPacks.Avalonia.RadixIcons.xml", "lib/net8.0/IconPacks.Avalonia.RadixIcons.dll", "lib/net8.0/IconPacks.Avalonia.RadixIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.RadixIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.RadixIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.RemixIcon/1.2.0": {"sha512": "CU0G6AMuiXw9U1LiOf/UEooIxd16cQypk44+JbG8H/rNVKFgAwhRCJKbkN+zYYe/MaM/fw0VaTOhkYqEbRu5ew==", "type": "package", "path": "iconpacks.avalonia.remixicon/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.remixicon.1.2.0.nupkg.sha512", "iconpacks.avalonia.remixicon.nuspec", "lib/net6.0/IconPacks.Avalonia.RemixIcon.dll", "lib/net6.0/IconPacks.Avalonia.RemixIcon.xml", "lib/net8.0/IconPacks.Avalonia.RemixIcon.dll", "lib/net8.0/IconPacks.Avalonia.RemixIcon.xml", "lib/netstandard2.0/IconPacks.Avalonia.RemixIcon.dll", "lib/netstandard2.0/IconPacks.Avalonia.RemixIcon.xml", "logo_small.png"]}, "IconPacks.Avalonia.RPGAwesome/1.2.0": {"sha512": "ad3oc5M36mzDQHKi7LeX7gNMKewY6QK/7JSxUOp/PIwpuJuenm7fRKEiGqkywixD87mGSZxL29H3DeXS8R9qrQ==", "type": "package", "path": "iconpacks.avalonia.rpgawesome/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.rpgawesome.1.2.0.nupkg.sha512", "iconpacks.avalonia.rpgawesome.nuspec", "lib/net6.0/IconPacks.Avalonia.RPGAwesome.dll", "lib/net6.0/IconPacks.Avalonia.RPGAwesome.xml", "lib/net8.0/IconPacks.Avalonia.RPGAwesome.dll", "lib/net8.0/IconPacks.Avalonia.RPGAwesome.xml", "lib/netstandard2.0/IconPacks.Avalonia.RPGAwesome.dll", "lib/netstandard2.0/IconPacks.Avalonia.RPGAwesome.xml", "logo_small.png"]}, "IconPacks.Avalonia.SimpleIcons/1.2.0": {"sha512": "9zP1jVKHG1cYmKIEeb3IcK0OEeNQKD0Ldw+uXrt5Bls1xY/0KSVcyhDFcejcOOiwjNufs356eEFuEeYtcwjFlw==", "type": "package", "path": "iconpacks.avalonia.simpleicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.simpleicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.simpleicons.nuspec", "lib/net6.0/IconPacks.Avalonia.SimpleIcons.dll", "lib/net6.0/IconPacks.Avalonia.SimpleIcons.xml", "lib/net8.0/IconPacks.Avalonia.SimpleIcons.dll", "lib/net8.0/IconPacks.Avalonia.SimpleIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.SimpleIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.SimpleIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Typicons/1.2.0": {"sha512": "6AudcDYMBqgf2xHIloxnnX6MFiszAneGv+qFlmw95R+vkapko80L2ZSFwviIfSle8mcXjk7pVlTknSaxtcqWhQ==", "type": "package", "path": "iconpacks.avalonia.typicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.typicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.typicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Typicons.dll", "lib/net6.0/IconPacks.Avalonia.Typicons.xml", "lib/net8.0/IconPacks.Avalonia.Typicons.dll", "lib/net8.0/IconPacks.Avalonia.Typicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Typicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Typicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Unicons/1.2.0": {"sha512": "8GogpnanIlyvJD5q/vxseE58Z+WPJ6zrJpd1+53cKWdlU2jxjkWSkIH2aWy0oEDDnR2b5v8D6Y2XxixXyxfe/w==", "type": "package", "path": "iconpacks.avalonia.unicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.unicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.unicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Unicons.dll", "lib/net6.0/IconPacks.Avalonia.Unicons.xml", "lib/net8.0/IconPacks.Avalonia.Unicons.dll", "lib/net8.0/IconPacks.Avalonia.Unicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Unicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Unicons.xml", "logo_small.png"]}, "IconPacks.Avalonia.VaadinIcons/1.2.0": {"sha512": "jUjRyqjuC9FCeBuhcaYi33rExDO+U69o04D6eG7g8sFTbHzhpdfqJYnTe6pUiWti54dCmFP8pGYVgRZ05fhfkg==", "type": "package", "path": "iconpacks.avalonia.vaadinicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.vaadinicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.vaadinicons.nuspec", "lib/net6.0/IconPacks.Avalonia.VaadinIcons.dll", "lib/net6.0/IconPacks.Avalonia.VaadinIcons.xml", "lib/net8.0/IconPacks.Avalonia.VaadinIcons.dll", "lib/net8.0/IconPacks.Avalonia.VaadinIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.VaadinIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.VaadinIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.WeatherIcons/1.2.0": {"sha512": "KpiJgWXyXRF0qk/MVfB0wZY3pVrBKAJCddp2j1nTuXKvdV2qEn6ursZGI12eLHrglGowMdm2A5pGhm7osHrBlw==", "type": "package", "path": "iconpacks.avalonia.weathericons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.weathericons.1.2.0.nupkg.sha512", "iconpacks.avalonia.weathericons.nuspec", "lib/net6.0/IconPacks.Avalonia.WeatherIcons.dll", "lib/net6.0/IconPacks.Avalonia.WeatherIcons.xml", "lib/net8.0/IconPacks.Avalonia.WeatherIcons.dll", "lib/net8.0/IconPacks.Avalonia.WeatherIcons.xml", "lib/netstandard2.0/IconPacks.Avalonia.WeatherIcons.dll", "lib/netstandard2.0/IconPacks.Avalonia.WeatherIcons.xml", "logo_small.png"]}, "IconPacks.Avalonia.Zondicons/1.2.0": {"sha512": "GKbYkW+NIM28j9/tkALHWtpLGrU8b7Qv3aHIUG0YH6Tyo3fveJesey2VJgjkH1hQBFUAQSKUAtAcI/H5W+urCg==", "type": "package", "path": "iconpacks.avalonia.zondicons/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "iconpacks.avalonia.zondicons.1.2.0.nupkg.sha512", "iconpacks.avalonia.zondicons.nuspec", "lib/net6.0/IconPacks.Avalonia.Zondicons.dll", "lib/net6.0/IconPacks.Avalonia.Zondicons.xml", "lib/net8.0/IconPacks.Avalonia.Zondicons.dll", "lib/net8.0/IconPacks.Avalonia.Zondicons.xml", "lib/netstandard2.0/IconPacks.Avalonia.Zondicons.dll", "lib/netstandard2.0/IconPacks.Avalonia.Zondicons.xml", "logo_small.png"]}, "MicroCom.Runtime/0.11.0": {"sha512": "MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "type": "package", "path": "microcom.runtime/0.11.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/MicroCom.Runtime.dll", "lib/netstandard2.0/MicroCom.Runtime.dll", "microcom.runtime.0.11.0.nupkg.sha512", "microcom.runtime.nuspec"]}, "OpenCvSharp4/4.8.0.20230708": {"sha512": "MmZqvNUJuTUWQfkyXN1NOxJjx6JNfaedMOnY0dAdP5zgaKMAsxwwX5pkZl/F40JheSo3oMJhI7nhDC3kh7/ISQ==", "type": "package", "path": "opencvsharp4/4.8.0.20230708", "files": [".nupkg.metadata", ".signature.p7s", "lib/net48/OpenCvSharp.dll", "lib/net48/OpenCvSharp.xml", "lib/net6.0/OpenCvSharp.dll", "lib/net6.0/OpenCvSharp.xml", "lib/netstandard2.0/OpenCvSharp.dll", "lib/netstandard2.0/OpenCvSharp.xml", "lib/netstandard2.1/OpenCvSharp.dll", "lib/netstandard2.1/OpenCvSharp.xml", "opencvsharp4.4.8.0.20230708.nupkg.sha512", "opencvsharp4.nuspec"]}, "OpenCvSharp4.runtime.win/4.8.0.20230708": {"sha512": "V9zbVQXazSSoEG04nF1KEkxfY62WMQAcKAlDe7xgkNErY7WQZzEcj8dMEuHXQMaDr45jPvrJi50SQroi+w4wNw==", "type": "package", "path": "opencvsharp4.runtime.win/4.8.0.20230708", "files": [".nupkg.metadata", ".signature.p7s", "build/net48/OpenCvSharp4.runtime.win.props", "build/net5.0/OpenCvSharp4.runtime.win.props", "build/netcoreapp/OpenCvSharp4.runtime.win.props", "build/netstandard/OpenCvSharp4.runtime.win.props", "opencvsharp4.runtime.win.4.8.0.20230708.nupkg.sha512", "opencvsharp4.runtime.win.nuspec", "runtimes/win-x64/native/OpenCvSharpExtern.dll", "runtimes/win-x64/native/opencv_videoio_ffmpeg480_64.dll", "runtimes/win-x86/native/OpenCvSharpExtern.dll", "runtimes/win-x86/native/opencv_videoio_ffmpeg480.dll"]}, "SkiaSharp/2.88.9": {"sha512": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "type": "package", "path": "skiasharp/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.9.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"sha512": "cWSaJKVPWAaT/WIn9c8T5uT/l4ETwHxNJTkEOtNKjphNo8AW6TF9O32aRkxqw3l8GUdUo66Bu7EiqtFh/XG0Zg==", "type": "package", "path": "skiasharp.nativeassets.linux/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.2.88.9.nupkg.sha512", "skiasharp.nativeassets.linux.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"sha512": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"sha512": "kt06RccBHSnAs2wDYdBSfsjIDbY3EpsOVqnlDgKdgvyuRA8ZFDaHRdWNx1VHjGgYzmnFCGiTJBnXFl5BqGwGnA==", "type": "package", "path": "skiasharp.nativeassets.webassembly/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libSkiaSharp.a/2.0.23/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/2.0.6/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt,simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.7/libSkiaSharp.a", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "lib/netstandard1.0/_._", "skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512", "skiasharp.nativeassets.webassembly.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"sha512": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "Tmds.DBus.Protocol/0.21.2": {"sha512": "ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "type": "package", "path": "tmds.dbus.protocol/0.21.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Tmds.DBus.Protocol.dll", "lib/net8.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.1/Tmds.DBus.Protocol.dll", "tmds.dbus.protocol.0.21.2.nupkg.sha512", "tmds.dbus.protocol.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["Avalonia >= 11.3.0", "Avalonia.Desktop >= 11.3.0", "Avalonia.Diagnostics >= 11.3.0", "Avalonia.Fonts.Inter >= 11.3.0", "Avalonia.Themes.Fluent >= 11.3.0", "CommunityToolkit.Mvvm >= 8.2.1", "IconPacks.Avalonia >= 1.2.0", "OpenCvSharp4 >= 4.8.0.20230708", "OpenCvSharp4.runtime.win >= 4.8.0.20230708", "SkiaSharp >= 2.88.9"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\AvaloniaApplication9.9-version3\\AvaloniaApplication2\\AvaloniaApplication2.csproj", "projectName": "AvaloniaApplication2", "projectPath": "E:\\AvaloniaApplication9.9-version3\\AvaloniaApplication2\\AvaloniaApplication2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\AvaloniaApplication9.9-version3\\AvaloniaApplication2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.1, )"}, "IconPacks.Avalonia": {"target": "Package", "version": "[1.2.0, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.8.0.20230708, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.8.0.20230708, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}