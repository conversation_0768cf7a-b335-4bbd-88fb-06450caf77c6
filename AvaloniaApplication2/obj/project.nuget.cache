{"version": 2, "dgSpecHash": "JjCUpQ2gdvE=", "success": true, "projectFilePath": "E:\\AvaloniaApplication9.9-version1\\AvaloniaApplication2\\AvaloniaApplication2.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\avalonia\\11.3.0\\avalonia.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.angle.windows.natives\\2.1.22045.20230930\\avalonia.angle.windows.natives.2.1.22045.20230930.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.buildservices\\0.0.31\\avalonia.buildservices.0.0.31.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.colorpicker\\11.3.0\\avalonia.controls.colorpicker.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.desktop\\11.3.0\\avalonia.desktop.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.diagnostics\\11.3.0\\avalonia.diagnostics.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.fonts.inter\\11.3.0\\avalonia.fonts.inter.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.freedesktop\\11.3.0\\avalonia.freedesktop.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.native\\11.3.0\\avalonia.native.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.remote.protocol\\11.3.0\\avalonia.remote.protocol.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.skia\\11.3.0\\avalonia.skia.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.fluent\\11.3.0\\avalonia.themes.fluent.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.simple\\11.3.0\\avalonia.themes.simple.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.win32\\11.3.0\\avalonia.win32.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.x11\\11.3.0\\avalonia.x11.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.2.1\\communitytoolkit.mvvm.8.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\7.3.0.3\\harfbuzzsharp.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.linux\\7.3.0.3\\harfbuzzsharp.nativeassets.linux.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\7.3.0.3\\harfbuzzsharp.nativeassets.macos.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.webassembly\\7.3.0.3\\harfbuzzsharp.nativeassets.webassembly.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\7.3.0.3\\harfbuzzsharp.nativeassets.win32.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia\\1.2.0\\iconpacks.avalonia.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.bootstrapicons\\1.2.0\\iconpacks.avalonia.bootstrapicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.boxicons\\1.2.0\\iconpacks.avalonia.boxicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.circumicons\\1.2.0\\iconpacks.avalonia.circumicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.codicons\\1.2.0\\iconpacks.avalonia.codicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.coolicons\\1.2.0\\iconpacks.avalonia.coolicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.core\\1.2.0\\iconpacks.avalonia.core.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.entypo\\1.2.0\\iconpacks.avalonia.entypo.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.evaicons\\1.2.0\\iconpacks.avalonia.evaicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.feathericons\\1.2.0\\iconpacks.avalonia.feathericons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fileicons\\1.2.0\\iconpacks.avalonia.fileicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fontaudio\\1.2.0\\iconpacks.avalonia.fontaudio.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fontawesome\\1.2.0\\iconpacks.avalonia.fontawesome.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fontawesome5\\1.2.0\\iconpacks.avalonia.fontawesome5.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fontawesome6\\1.2.0\\iconpacks.avalonia.fontawesome6.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.fontisto\\1.2.0\\iconpacks.avalonia.fontisto.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.forkawesome\\1.2.0\\iconpacks.avalonia.forkawesome.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.gameicons\\1.2.0\\iconpacks.avalonia.gameicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.ionicons\\1.2.0\\iconpacks.avalonia.ionicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.jamicons\\1.2.0\\iconpacks.avalonia.jamicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.keyruneicons\\1.2.0\\iconpacks.avalonia.keyruneicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.lucide\\1.2.0\\iconpacks.avalonia.lucide.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.material\\1.2.0\\iconpacks.avalonia.material.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.materialdesign\\1.2.0\\iconpacks.avalonia.materialdesign.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.materiallight\\1.2.0\\iconpacks.avalonia.materiallight.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.memoryicons\\1.2.0\\iconpacks.avalonia.memoryicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.microns\\1.2.0\\iconpacks.avalonia.microns.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.mingcuteicons\\1.2.0\\iconpacks.avalonia.mingcuteicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.modern\\1.2.0\\iconpacks.avalonia.modern.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.mynauiicons\\1.2.0\\iconpacks.avalonia.mynauiicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.octicons\\1.2.0\\iconpacks.avalonia.octicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.phosphoricons\\1.2.0\\iconpacks.avalonia.phosphoricons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.picolicons\\1.2.0\\iconpacks.avalonia.picolicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.pixelarticons\\1.2.0\\iconpacks.avalonia.pixelarticons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.radixicons\\1.2.0\\iconpacks.avalonia.radixicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.remixicon\\1.2.0\\iconpacks.avalonia.remixicon.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.rpgawesome\\1.2.0\\iconpacks.avalonia.rpgawesome.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.simpleicons\\1.2.0\\iconpacks.avalonia.simpleicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.typicons\\1.2.0\\iconpacks.avalonia.typicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.unicons\\1.2.0\\iconpacks.avalonia.unicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.vaadinicons\\1.2.0\\iconpacks.avalonia.vaadinicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.weathericons\\1.2.0\\iconpacks.avalonia.weathericons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\iconpacks.avalonia.zondicons\\1.2.0\\iconpacks.avalonia.zondicons.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microcom.runtime\\0.11.0\\microcom.runtime.0.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4\\4.8.0.20230708\\opencvsharp4.4.8.0.20230708.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4.runtime.win\\4.8.0.20230708\\opencvsharp4.runtime.win.4.8.0.20230708.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.9\\skiasharp.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux\\2.88.9\\skiasharp.nativeassets.linux.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.9\\skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.webassembly\\2.88.9\\skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.9\\skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tmds.dbus.protocol\\0.21.2\\tmds.dbus.protocol.0.21.2.nupkg.sha512"], "logs": []}