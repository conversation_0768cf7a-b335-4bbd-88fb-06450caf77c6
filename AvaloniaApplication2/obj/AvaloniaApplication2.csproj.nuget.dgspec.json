{"format": 1, "restore": {"E:\\AvaloniaApplication9.9 - 副本\\AvaloniaApplication2\\AvaloniaApplication2.csproj": {}}, "projects": {"E:\\AvaloniaApplication9.9 - 副本\\AvaloniaApplication2\\AvaloniaApplication2.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\AvaloniaApplication9.9 - 副本\\AvaloniaApplication2\\AvaloniaApplication2.csproj", "projectName": "AvaloniaApplication2", "projectPath": "E:\\AvaloniaApplication9.9 - 副本\\AvaloniaApplication2\\AvaloniaApplication2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\AvaloniaApplication9.9 - 副本\\AvaloniaApplication2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.1, )"}, "IconPacks.Avalonia": {"target": "Package", "version": "[1.2.0, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.8.0.20230708, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.8.0.20230708, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}