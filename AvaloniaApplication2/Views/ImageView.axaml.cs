using System;
using System.Collections.Generic;
using System.ComponentModel;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Controls.Primitives;
using Avalonia.Media.Imaging;
using SkiaSharp; // for pixel sampling (cross-platform)
using System.IO;
using Avalonia.Controls.Shapes;
using AvaloniaApplication2.Models;

using Avalonia.Media;
using Avalonia.Interactivity;
using Avalonia.VisualTree;
using Avalonia.Threading;
using AvaloniaApplication2.ViewModels;
using AvaloniaApplication2.Services;
using ImageViewer.Controls;
using static ImageViewer.Controls.RenderView;
using ImageViewer.Controls;
using ImageViewer.Drawing;
using System.Linq;

namespace AvaloniaApplication2.Views
{
    public partial class ImageView : UserControl
    {
        // 移除ScrollViewer，现在使用RenderView内部缩放
        private Image? _mainImage;
        private Canvas? _roiCanvas;
        private Canvas? _navCanvas;
        private Border? _navRect;
        private Control? _navHost;
        private RenderView? _renderView;
        private Grid? _legacyCenterGrid;

        private bool _isDragging;
        private Point _dragStart;
        private double _startVX, _startVY;
        private bool _hasFitForCurrentImage;

        private DispatcherTimer? _navRepeatTimer;
        private int _navRepeatDelta; // -1 or +1
        private Button? _prevButton;
        private Button? _nextButton;
        private System.ComponentModel.INotifyPropertyChanged? _galleryNotify;

        private ToggleButton? _btnPencil;
        private ToggleButton? _btnEraser;
        private ToggleButton? _btnPolygon;
        private ToggleButton? _btnRect;
        private ToggleButton? _btnCircle;
        private ToggleButton? _btnLine;
        private ToggleButton? _btnClean;
        // Cached Skia bitmap for sampling RGB without unsafe/Lock
        private SKBitmap? _skSampleBitmap;
        private string? _skSampleBitmapPath;
        // Per-tool sizes
        private double _drawThickness = 2.0; // deprecated global, keep for backward compatibility
        private double _eraserSize = 20.5;   // midpoint default for eraser (1~40)
        // Per-tool independent thickness & color (defaults: thinner, red for pencil)
        private double _pencilThickness = 5.1; // slider midpoint between 0.2 and 10
        private double _polygonThickness = 1.0;
        private double _rectThickness = 1.0;
        private double _circleThickness = 1.0;
        private double _lineThickness = 1.0;

        private IBrush _pencilColor = Brushes.Red;
        private IBrush _polygonColor = Brushes.Red;
        private IBrush _rectColor = Brushes.Red;
        private IBrush _circleColor = Brushes.Red;
        private IBrush _lineColor = Brushes.Red;

        public ImageView()
        {
            InitializeComponent();
            this.AttachedToVisualTree += OnAttachedToVisualTree;
            this.DetachedFromVisualTree += OnDetachedFromVisualTree;
            this.KeyDown += OnRootKeyDown;
            this.DataContextChanged += OnDataContextChanged;

            _navRepeatTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(120) };
            _navRepeatTimer.Tick += (_, __) => NavigateBy(_navRepeatDelta);
        }

        private void OnToolEraserClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                // Keep service in Pan or Edit; erasing handled in RenderView
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Pan;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Pan);
                this.Cursor = new Cursor(StandardCursorType.Hand);
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.Eraser;
                    _renderView.StrokeThickness = _eraserSize; // use separate eraser size without changing VM thickness
                }
                if (_navHost != null) _navHost.IsHitTestVisible = false;
                UpdateToolButtons(_btnEraser);
            }
        }

        // ==== Flyout Opened: sync slider positions and color previews ====
        private static void TrySetPreviewFromSender(object? sender, string previewName, IBrush color)
        {
            if (sender is not Control c) return;
            var container = c.FindAncestorOfType<Control>();
            if (container == null) return;
            var preview = container.FindControl<Border>(previewName);
            if (preview != null) preview.Background = color;
        }

        private static void SetSelectedColorFrame(Control root, string gridName, IBrush selected)
        {
            var grid = root.FindControl<UniformGrid>(gridName);
            if (grid == null) return;
            foreach (var child in grid.GetVisualChildren())
            {
                if (child is Button btn)
                {
                    bool isSelected = false;
                    if (btn.Background is ISolidColorBrush bb && selected is ISolidColorBrush sb)
                    {
                        isSelected = bb.Color == sb.Color;
                    }
                    btn.BorderThickness = isSelected ? new Thickness(2) : new Thickness(0);
                    btn.BorderBrush = isSelected ? new SolidColorBrush(Color.FromArgb(160, 255, 255, 255)) : null;
                    btn.Opacity = isSelected ? 0.95 : 1.0;
                }
            }
        }
        private void OnPencilFlyoutOpened(object? sender, EventArgs e)
        {
            if (sender is not Flyout fly) return;
            if (fly.Content is not Control root) return;
            var slider = root.FindControl<Slider>("PencilSizeSlider");
            if (slider != null) slider.Value = _pencilThickness;
            var preview = root.FindControl<Border>("PencilColorPreview");
            if (preview != null) preview.Background = _pencilColor;
            SetSelectedColorFrame(root, "PencilColorsGrid", _pencilColor);
        }

        private void OnPolygonFlyoutOpened(object? sender, EventArgs e)
        {
            if (sender is not Flyout fly) return;
            if (fly.Content is not Control root) return;
            var slider = root.FindControl<Slider>("PolygonSizeSlider");
            if (slider != null) slider.Value = _polygonThickness;
            var preview = root.FindControl<Border>("PolygonColorPreview");
            if (preview != null) preview.Background = _polygonColor;
            SetSelectedColorFrame(root, "PolygonColorsGrid", _polygonColor);
        }

        private void OnRectangleFlyoutOpened(object? sender, EventArgs e)
        {
            if (sender is not Flyout fly) return;
            if (fly.Content is not Control root) return;
            var slider = root.FindControl<Slider>("RectangleSizeSlider");
            if (slider != null) slider.Value = _rectThickness;
            var preview = root.FindControl<Border>("RectangleColorPreview");
            if (preview != null) preview.Background = _rectColor;
            SetSelectedColorFrame(root, "RectangleColorsGrid", _rectColor);
        }

        private void OnEraserFlyoutOpened(object? sender, EventArgs e)
        {
            if (sender is not Flyout fly) return;
            if (fly.Content is not Control root) return;
            var slider = root.FindControl<Slider>("EraserSizeSlider");
            if (slider != null) slider.Value = _eraserSize;
        }

        // Apply style (thickness & color) for the given tool to VM and RenderView
        private void ApplyStyle(AvaloniaApplication2.Models.ToolMode mode)
        {
            if (_renderView == null) return;
            if (DataContext is not ImageViewModel vm) return;
            switch (mode)
            {
                case AvaloniaApplication2.Models.ToolMode.Pencil:
                    vm.DrawingService.StrokeThickness = _pencilThickness;
                    vm.DrawingService.Stroke = _pencilColor;
                    _renderView.StrokeThickness = _pencilThickness;
                    _renderView.Stroke = _pencilColor;
                    break;
                case AvaloniaApplication2.Models.ToolMode.Polygon:
                    vm.DrawingService.StrokeThickness = _polygonThickness;
                    vm.DrawingService.Stroke = _polygonColor;
                    _renderView.StrokeThickness = _polygonThickness;
                    _renderView.Stroke = _polygonColor;
                    break;
                case AvaloniaApplication2.Models.ToolMode.Rectangle:
                    vm.DrawingService.StrokeThickness = _rectThickness;
                    vm.DrawingService.Stroke = _rectColor;
                    _renderView.StrokeThickness = _rectThickness;
                    _renderView.Stroke = _rectColor;
                    break;
                case AvaloniaApplication2.Models.ToolMode.Circle:
                    vm.DrawingService.StrokeThickness = _circleThickness;
                    vm.DrawingService.Stroke = _circleColor;
                    _renderView.StrokeThickness = _circleThickness;
                    _renderView.Stroke = _circleColor;
                    break;
                case AvaloniaApplication2.Models.ToolMode.Line:
                    vm.DrawingService.StrokeThickness = _lineThickness;
                    vm.DrawingService.Stroke = _lineColor;
                    _renderView.StrokeThickness = _lineThickness;
                    _renderView.Stroke = _lineColor;
                    break;
            }
        }

        // ==== Per-tool size handlers (flyouts) ====
        private void OnDrawSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _drawThickness = e.NewValue;
            if (DataContext is ImageViewModel vm)
            {
                // Only apply to RenderView + VM when pencil tool is active
                if (_renderView != null && (
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawFreehand ||
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawPolygon ||
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawRect ||
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawCircle ||
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawLine))
                {
                    vm.DrawingService.StrokeThickness = _drawThickness;
                    _renderView.StrokeThickness = _drawThickness;
                    _renderView.InvalidateVisual();
                }
            }
        }

        private void OnEraserSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _eraserSize = e.NewValue;
            // 仅在正在使用橡皮时立即更新 RenderView 半径，避免影响形状线宽
            if (_renderView != null && _renderView.Tool == ImageViewer.Drawing.ToolMode.Eraser)
            {
                _renderView.StrokeThickness = _eraserSize;
                _renderView.InvalidateVisual();
            }
        }

        // ---- Pencil flyout ----
        private void OnPencilSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _pencilThickness = e.NewValue;
            if (DataContext is ImageViewModel vm)
            {
                // 立即更新RenderView的StrokeThickness，检查多种可能的画笔工具状态
                if (_renderView != null && (
                    _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawFreehand ||
                    vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Pencil))
                {
                    _renderView.StrokeThickness = _pencilThickness;
                    vm.DrawingService.StrokeThickness = _pencilThickness;
                    _renderView.InvalidateVisual();
                }

                // 如果当前工具是画笔，也应用样式
                if (vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Pencil)
                    ApplyStyle(vm.CurrentTool);
            }
        }
        private void OnPencilColorPick(object? sender, RoutedEventArgs e)
        {
            if (sender is Button b && b.Tag is string s)
            {
                try { _pencilColor = Brush.Parse(s); }
                catch { _pencilColor = Brushes.Red; }
                TrySetPreviewFromSender(sender, "PencilColorPreview", _pencilColor);
                if (b.FindAncestorOfType<FlyoutPresenter>() is { } pres)
                {
                    SetSelectedColorFrame(pres, "PencilColorsGrid", _pencilColor);
                }
                if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Pencil)
                    ApplyStyle(vm.CurrentTool);
            }
        }

        // ---- Polygon flyout ----
        private void OnPolygonSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _polygonThickness = e.NewValue;
            if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Polygon)
                ApplyStyle(vm.CurrentTool);
        }
        private void OnPolygonColorPick(object? sender, RoutedEventArgs e)
        {
            if (sender is Button b && b.Tag is string s)
            {
                try { _polygonColor = Brush.Parse(s); }
                catch { _polygonColor = Brushes.Orange; }
                TrySetPreviewFromSender(sender, "PolygonColorPreview", _polygonColor);
                if (b.FindAncestorOfType<FlyoutPresenter>() is { } pres)
                {
                    SetSelectedColorFrame(pres, "PolygonColorsGrid", _polygonColor);
                }
                if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Polygon)
                    ApplyStyle(vm.CurrentTool);
            }
        }

        // ---- Rectangle flyout ----
        private void OnRectangleSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _rectThickness = e.NewValue;
            if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Rectangle)
                ApplyStyle(vm.CurrentTool);
        }
        private void OnRectangleColorPick(object? sender, RoutedEventArgs e)
        {
            if (sender is Button b && b.Tag is string s)
            {
                try { _rectColor = Brush.Parse(s); }
                catch { _rectColor = Brushes.Lime; }
                TrySetPreviewFromSender(sender, "RectangleColorPreview", _rectColor);
                if (b.FindAncestorOfType<FlyoutPresenter>() is { } pres)
                {
                    SetSelectedColorFrame(pres, "RectangleColorsGrid", _rectColor);
                }
                if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Rectangle)
                    ApplyStyle(vm.CurrentTool);
            }
        }

        // ---- Circle flyout ----
        private void OnCircleSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _circleThickness = e.NewValue;
            if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Circle)
                ApplyStyle(vm.CurrentTool);
        }
        private void OnCircleColorPick(object? sender, RoutedEventArgs e)
        {
            if (sender is Button b && b.Tag is string s)
            {
                try { _circleColor = Brush.Parse(s); }
                catch { _circleColor = Brushes.Cyan; }
                TrySetPreviewFromSender(sender, "CircleColorPreview", _circleColor);
                if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Circle)
                    ApplyStyle(vm.CurrentTool);
            }
        }

        // ---- Line flyout ----
        private void OnLineSizeChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            _lineThickness = e.NewValue;
            if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Line)
                ApplyStyle(vm.CurrentTool);
        }
        private void OnLineColorPick(object? sender, RoutedEventArgs e)
        {
            if (sender is Button b && b.Tag is string s)
            {
                try { _lineColor = Brush.Parse(s); }
                catch { _lineColor = Brushes.White; }
                TrySetPreviewFromSender(sender, "LineColorPreview", _lineColor);
                if (DataContext is ImageViewModel vm && vm.CurrentTool == AvaloniaApplication2.Models.ToolMode.Line)
                    ApplyStyle(vm.CurrentTool);
            }
        }

        // 旧的旋转矩形光标映射方法已移除，现在由 RenderView 控件处理

        private void ImageView_Loaded(object? sender, RoutedEventArgs e)
        {
            // Focus to receive key events
            this.Focus();
            UpdateArrowEnableStates();
            _renderView?.InvalidateVisual();
        }

        // 移除旧的滚轮缩放逻辑，现在由RenderView内部处理
        // 如果需要Ctrl+滚轮的特殊处理，可以在这里添加

        private void OnAttachedToVisualTree(object? sender, VisualTreeAttachmentEventArgs e)
        {
            // _mainScroll 已移除，现在使用RenderView内部缩放
            _mainImage = this.FindControl<Image>("MainImage");
            _roiCanvas = this.FindControl<Canvas>("RoiCanvas");
            _navCanvas = this.FindControl<Canvas>("NavigatorCanvas");
            _navRect = this.FindControl<Border>("NavigatorRect");
            _navHost = this.FindControl<Control>("NavigatorHost");
            _prevButton = this.FindControl<Button>("PrevButton");
            _nextButton = this.FindControl<Button>("NextButton");
            // Toolbar toggle buttons
            _btnPencil = this.FindControl<ToggleButton>("ToolPencilButton");
            _btnEraser = this.FindControl<ToggleButton>("ToolEraserButton");
            _btnPolygon = this.FindControl<ToggleButton>("ToolPolygonButton");
            _btnRect = this.FindControl<ToggleButton>("ToolRectangleButton");
            // Note: XAML names 'ToolCirleButton' (typo), match it exactly here
            _btnCircle = this.FindControl<ToggleButton>("ToolCirleButton");
            _btnLine = this.FindControl<ToggleButton>("ToolLineButton");
            _btnClean = this.FindControl<ToggleButton>("ToolCleanButton");
            _renderView = this.FindControl<RenderView>("RenderView");
            _legacyCenterGrid = this.FindControl<Grid>("LegacyCenterGrid");

            if (_renderView != null)
            {
                _renderView.PropertyChanged += OnRenderViewPropertyChanged;
                _renderView.PixelInfoChanged += OnRenderViewPixelInfoChanged;
                _renderView.ShapeSelectionChanged += OnRenderViewShapeSelectionChanged;
                // 绑定 RenderView 指针事件用于更新状态栏
                _renderView.PointerMoved += OnRenderViewPointerMoved;
                _renderView.PointerPressed += OnRenderViewPointerPressed;
                _renderView.PointerReleased += OnRenderViewPointerReleased;
                // 监听滚轮事件来更新导航器
                _renderView.PointerWheelChanged += OnRenderViewWheelChanged;
            }

            // ScrollViewer 事件处理已移除
            // MainImage and RoiCanvas no longer used; RenderView handles both image and overlays

            // Wire drawing service hooks
            if (DataContext is ImageViewModel vm)
            {
                var svc = vm.DrawingService;
                // 平移现在由RenderView内部处理，不需要外部平移请求
                svc.RequestPanBy = v => false;
                svc.InvalidateRequested = () => _renderView?.InvalidateVisual();

                // Initialize per-image ROI context for the first image
                var key = string.IsNullOrWhiteSpace(vm.DisplayImagePath) ? vm.CurrentImagePath : vm.DisplayImagePath;
                if (!string.IsNullOrWhiteSpace(key))
                {
                    svc.SwitchImage(key);
                }
            }

            if (DataContext is INotifyPropertyChanged inpc)
            {
                inpc.PropertyChanged += OnViewModelPropertyChanged;
            }

            UpdateViewportValues();
            // Try an initial fit
            Dispatcher.UIThread.Post(FitToViewport, DispatcherPriority.Background);

            // Default to Pencil tool
            if (DataContext is ImageViewModel vmInit)
            {
                vmInit.CurrentTool = AvaloniaApplication2.Models.ToolMode.Pencil;
                vmInit.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Pencil);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawFreehand;
                    ApplyStyle(vmInit.CurrentTool);
                }
            }
            UpdateToolButtons(_btnPencil);
        }

        private void UpdateToolButtons(ToggleButton? active)
        {
            // Uncheck all tool buttons
            if (_btnPencil != null) _btnPencil.IsChecked = false;
            if (_btnEraser != null) _btnEraser.IsChecked = false;
            if (_btnPolygon != null) _btnPolygon.IsChecked = false;
            if (_btnRect != null) _btnRect.IsChecked = false;
            if (_btnCircle != null) _btnCircle.IsChecked = false;
            if (_btnLine != null) _btnLine.IsChecked = false;
            if (_btnClean != null) _btnClean.IsChecked = false; // action button stays unchecked

            if (active != null) active.IsChecked = true;
        }

        private void OnDetachedFromVisualTree(object? sender, VisualTreeAttachmentEventArgs e)
        {
            // ScrollViewer 事件解绑已移除
            if (_navRepeatTimer != null)
            {
                _navRepeatTimer.Stop();
            }
            if (_galleryNotify != null)
            {
                _galleryNotify.PropertyChanged -= OnGalleryPropertyChanged;
                _galleryNotify = null;
            }
            // ScrollViewer 事件解绑已移除
            if (_renderView != null)
            {
                _renderView.PropertyChanged -= OnRenderViewPropertyChanged;
                _renderView.PixelInfoChanged -= OnRenderViewPixelInfoChanged;
                _renderView.ShapeSelectionChanged -= OnRenderViewShapeSelectionChanged;
                _renderView.PointerMoved -= OnRenderViewPointerMoved;
                _renderView.PointerPressed -= OnRenderViewPointerPressed;
                _renderView.PointerReleased -= OnRenderViewPointerReleased;
                _renderView.PointerWheelChanged -= OnRenderViewWheelChanged;
            }
            if (DataContext is INotifyPropertyChanged inpc)
            {
                inpc.PropertyChanged -= OnViewModelPropertyChanged;
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ImageViewModel.CurrentImagePath) ||
                     e.PropertyName == nameof(ImageViewModel.DisplayImagePath))
            {
                _hasFitForCurrentImage = false;
                Dispatcher.UIThread.Post(FitToViewport, DispatcherPriority.Background);
                // Switch ROI set to match the new image
                if (sender is ImageViewModel vm)
                {
                    var key = string.IsNullOrWhiteSpace(vm.DisplayImagePath) ? vm.CurrentImagePath : vm.DisplayImagePath;
                    if (!string.IsNullOrWhiteSpace(key))
                    {
                        vm.DrawingService.SwitchImage(key);
                    }
                }
            }
            else if (e.PropertyName == nameof(ImageViewModel.PreviewZoom))
            {
                // 同步导航器缩放到RenderView
                if (sender is ImageViewModel vm && _renderView != null)
                {
                    // 更新RenderView的Zoom属性（用于外部同步）
                    _renderView.Zoom = vm.PreviewZoom;

                    // 更新RenderView的内部缩放系统
                    _renderView.SetViewScale(vm.PreviewZoom);

                    // 更新视口值和导航器矩形
                    UpdateViewportValues();
                    UpdateNavigatorRect();
                }
            }
            else if (e.PropertyName == nameof(ImageViewModel.CurrentTool))
            {
                if (sender is ImageViewModel vm)
                {
                    ToggleButton? activeButton = vm.CurrentTool switch
                    {
                        AvaloniaApplication2.Models.ToolMode.Pencil => _btnPencil,
                        AvaloniaApplication2.Models.ToolMode.Polygon => _btnPolygon,
                        AvaloniaApplication2.Models.ToolMode.Rectangle => _btnRect,
                        AvaloniaApplication2.Models.ToolMode.Circle => _btnCircle,
                        AvaloniaApplication2.Models.ToolMode.Line => _btnLine,
                        _ => null
                    };
                    UpdateToolButtons(activeButton);

                    // 工具切换时立即应用样式，确保粗细和颜色正确同步
                    ApplyStyle(vm.CurrentTool);
                }
            }
        }

        // ScrollViewer 相关事件处理器已移除

        private void OnRenderViewPropertyChanged(object? sender, AvaloniaPropertyChangedEventArgs e)
        {
            if (sender is not RenderView rv) return;
            if (e.Property == RenderView.SourceProperty)
            {
                _hasFitForCurrentImage = false;
                UpdateImageBoundsFromRenderView();
                _renderView?.InvalidateVisual();
            }
            else if (e.Property == RenderView.ZoomProperty)
            {
                // 同步RenderView缩放到导航器PreviewZoom
                if (DataContext is ImageViewModel vm)
                {
                    vm.PreviewZoom = rv.Zoom;
                }
            }
            // 延迟更新导航器，避免在绘制过程中频繁更新
            Dispatcher.UIThread.Post(() =>
            {
                UpdateViewportValues();
                UpdateNavigatorRect();
            }, DispatcherPriority.Background);

            if (!_hasFitForCurrentImage)
                Dispatcher.UIThread.Post(FitToViewport, DispatcherPriority.Background);
        }

        private void UpdateViewportValues()
        {
            if (DataContext is not ImageViewModel vm || _renderView?.Source == null) return;

            try
            {
                // 计算当前视口在图像中的相对位置和大小
                var renderBounds = _renderView.Bounds;
                var source = _renderView.Source;

                if (renderBounds.Width <= 0 || renderBounds.Height <= 0 ||
                    source.Size.Width <= 0 || source.Size.Height <= 0) return;

                // 获取RenderView的当前缩放和偏移
                var scale = _renderView.GetViewScale();
                var offset = _renderView.GetViewOffset();
                var imageSize = source.Size;
                var scaledImageWidth = imageSize.Width * scale;
                var scaledImageHeight = imageSize.Height * scale;

                // 防止除零错误
                if (scale <= 0 || imageSize.Width <= 0 || imageSize.Height <= 0) return;

            // 计算视口相对于图像的位置和大小
            if (scaledImageWidth <= renderBounds.Width)
            {
                vm.ViewportX = 0.0;
                vm.ViewportW = 1.0;
            }
            else
            {
                // 计算水平方向的视口
                var visibleWidth = renderBounds.Width / scale;
                vm.ViewportW = Math.Min(1.0, visibleWidth / imageSize.Width);

                // 计算当前偏移对应的视口位置
                var normalizedOffsetX = -offset.X / scaledImageWidth;
                vm.ViewportX = Math.Max(0.0, Math.Min(1.0 - vm.ViewportW, normalizedOffsetX));
            }

            if (scaledImageHeight <= renderBounds.Height)
            {
                vm.ViewportY = 0.0;
                vm.ViewportH = 1.0;
            }
            else
            {
                // 计算垂直方向的视口
                var visibleHeight = renderBounds.Height / scale;
                vm.ViewportH = Math.Min(1.0, visibleHeight / imageSize.Height);

                // 计算当前偏移对应的视口位置
                var normalizedOffsetY = -offset.Y / scaledImageHeight;
                vm.ViewportY = Math.Max(0.0, Math.Min(1.0 - vm.ViewportH, normalizedOffsetY));
            }
            }
            catch (Exception)
            {
                // 如果计算过程中出现异常，使用默认值
                vm.ViewportX = 0.0;
                vm.ViewportY = 0.0;
                vm.ViewportW = 1.0;
                vm.ViewportH = 1.0;
            }
        }

        private void FitToViewport()
        {
            // 现在直接使用RenderView的公共适配方法
            if (_renderView != null)
            {
                _renderView.FitImage();
                _hasFitForCurrentImage = true;
            }
        }

        private void SyncScrollToViewport()
        {
            // 将视口变化同步到RenderView的偏移
            if (DataContext is not ImageViewModel vm || _renderView?.Source == null) return;

            var source = _renderView.Source;
            var scale = _renderView.GetViewScale();

            // 计算目标偏移
            var scaledImageWidth = source.Size.Width * scale;
            var scaledImageHeight = source.Size.Height * scale;

            var targetOffsetX = -vm.ViewportX * scaledImageWidth;
            var targetOffsetY = -vm.ViewportY * scaledImageHeight;

            // 使用公开方法设置RenderView的偏移
            _renderView.SetViewOffset(new Avalonia.Point(targetOffsetX, targetOffsetY));

            // 更新导航器矩形
            UpdateNavigatorRect();
        }

        private void UpdateImageBoundsFromRenderView()
        {
            if (_renderView?.Source is Bitmap bmpRender && DataContext is ImageViewModel vm2)
            {
                vm2.DrawingService.SetImageBounds(new Rect(0, 0, bmpRender.PixelSize.Width, bmpRender.PixelSize.Height));
            }
        }

        private void CenterScrollToNormalized(double cx, double cy)
        {
            // 将归一化坐标转换为视口位置并居中
            if (DataContext is not ImageViewModel vm || _renderView?.Source == null) return;

            var source = _renderView.Source;
            var scale = _renderView.GetViewScale();
            var renderBounds = _renderView.Bounds;

            // 计算在当前缩放下的视口大小
            var visibleWidth = renderBounds.Width / scale;
            var visibleHeight = renderBounds.Height / scale;

            // 计算视口相对大小
            var viewportW = Math.Min(1.0, visibleWidth / source.Size.Width);
            var viewportH = Math.Min(1.0, visibleHeight / source.Size.Height);

            // 计算居中位置
            var targetX = cx - viewportW / 2.0;
            var targetY = cy - viewportH / 2.0;

            // 限制在有效范围内
            vm.ViewportX = Math.Max(0.0, Math.Min(1.0 - viewportW, targetX));
            vm.ViewportY = Math.Max(0.0, Math.Min(1.0 - viewportH, targetY));
            vm.ViewportW = viewportW;
            vm.ViewportH = viewportH;

            // 同步到RenderView
            SyncScrollToViewport();
        }

        private void UpdateNavigatorRect()
        {
            if (DataContext is not ImageViewModel vm || _navCanvas == null || _navRect == null) return;

            var canvasWidth = _navCanvas.Bounds.Width;
            var canvasHeight = _navCanvas.Bounds.Height;

            if (canvasWidth <= 0 || canvasHeight <= 0) return;

            // 计算导航器矩形的位置和大小
            var rectLeft = vm.ViewportX * canvasWidth;
            var rectTop = vm.ViewportY * canvasHeight;
            var rectWidth = vm.ViewportW * canvasWidth;
            var rectHeight = vm.ViewportH * canvasHeight;

            // 设置矩形位置和大小
            Canvas.SetLeft(_navRect, rectLeft);
            Canvas.SetTop(_navRect, rectTop);
            _navRect.Width = rectWidth;
            _navRect.Height = rectHeight;
        }

        // ===== Navigator interactions =====
        private void OnNavigatorPointerPressed(object? sender, PointerPressedEventArgs e)
        {
            if (DataContext is not ImageViewModel vm) return;
            var p = e.GetPosition(_navCanvas);
            var rect = _navRect;
            if (_navCanvas == null || rect == null) return;

            double left = Canvas.GetLeft(rect);
            double top = Canvas.GetTop(rect);
            double w = rect.Bounds.Width;
            double h = rect.Bounds.Height;
            var r = new Rect(left, top, w, h);
            if (r.Contains(p))
            {
                _isDragging = true;
                _dragStart = p;
                _startVX = vm.ViewportX;
                _startVY = vm.ViewportY;
                rect.Cursor = new Cursor(StandardCursorType.SizeAll);
                e.Pointer.Capture(_navCanvas);
                e.Handled = true;
            }
            else
            {
                // Click empty area: center viewport at pointer
                double nx = p.X / _navCanvas.Bounds.Width;
                double ny = p.Y / _navCanvas.Bounds.Height;
                CenterScrollToNormalized(nx, ny);
                e.Handled = true;
            }
        }

        private void OnNavigatorPointerMoved(object? sender, PointerEventArgs e)
        {
            if (!_isDragging || _navCanvas == null) return;
            if (DataContext is not ImageViewModel vm) return;
            var p = e.GetPosition(_navCanvas);
            double dx = p.X - _dragStart.X;
            double dy = p.Y - _dragStart.Y;
            double nx = _startVX + dx / _navCanvas.Bounds.Width;
            double ny = _startVY + dy / _navCanvas.Bounds.Height;
            vm.ViewportX = Math.Clamp(nx, 0, 1 - vm.ViewportW);
            vm.ViewportY = Math.Clamp(ny, 0, 1 - vm.ViewportH);
            SyncScrollToViewport();
            e.Handled = true;
        }

        private void OnNavigatorPointerReleased(object? sender, PointerReleasedEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                e.Pointer.Capture(null);
                if (_navRect != null) _navRect.Cursor = new Cursor(StandardCursorType.Arrow);
                e.Handled = true;
            }
        }

        private void OnNavigatorWheelChanged(object? sender, PointerWheelEventArgs e)
        {
            // 导航器滚轮缩放已移除，现在由RenderView处理
            // 可以选择在这里不处理，让事件冒泡到RenderView
            e.Handled = false;
        }

        private void OnFitToViewportClick(object? sender, RoutedEventArgs e)
        {
            FitToViewport();
        }

        private void OnDataContextChanged(object? sender, EventArgs e)
        {
            if (_galleryNotify != null)
            {
                _galleryNotify.PropertyChanged -= OnGalleryPropertyChanged;
                _galleryNotify = null;
            }
            if (DataContext is ImageViewModel vm && vm.GalleryRef is System.ComponentModel.INotifyPropertyChanged npc)
            {
                _galleryNotify = npc;
                _galleryNotify.PropertyChanged += OnGalleryPropertyChanged;
            }
            UpdateArrowEnableStates();
        }

        private void OnGalleryPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(GalleryViewModel.CurrentItem) || e.PropertyName == nameof(GalleryViewModel.GalleryItems))
            {
                UpdateArrowEnableStates();
            }
        }

        private void UpdateArrowEnableStates()
        {
            if (DataContext is not ImageViewModel vm || vm.GalleryRef == null)
            {
                if (_prevButton != null) _prevButton.IsEnabled = false;
                if (_nextButton != null) _nextButton.IsEnabled = false;
                return;
            }
            var items = vm.GalleryRef.GalleryItems;
            if (items == null || items.Count == 0)
            {
                if (_prevButton != null) _prevButton.IsEnabled = false;
                if (_nextButton != null) _nextButton.IsEnabled = false;
                return;
            }
            // Wrap-around: as long as we have more than one item, both buttons are enabled
            bool canNavigate = items.Count > 1;
            if (_prevButton != null) _prevButton.IsEnabled = canNavigate;
            if (_nextButton != null) _nextButton.IsEnabled = canNavigate;
        }

        private void NavigateBy(int delta)
        {
            if (DataContext is not ImageViewModel vm) return;
            var gallery = vm.GalleryRef;
            if (gallery == null) return;
            var items = gallery.GalleryItems;
            if (items == null || items.Count == 0) return;

            int currentIndex = 0;
            if (gallery.CurrentItem != null)
            {
                currentIndex = items.IndexOf(gallery.CurrentItem);
            }
            else if (!string.IsNullOrWhiteSpace(vm.CurrentImagePath))
            {
                int idx = -1;
                for (int i = 0; i < items.Count; i++)
                {
                    if (string.Equals(items[i].ImagePath, vm.CurrentImagePath, StringComparison.OrdinalIgnoreCase))
                    {
                        idx = i;
                        break;
                    }
                }
                if (idx >= 0) currentIndex = idx;
            }

            // Wrap-around navigation
            int count = items.Count;
            int next = ((currentIndex + delta) % count + count) % count;
            if (next == currentIndex && count <= 1) return;

            var target = items[next];
            gallery.CurrentItem = target; // keep gallery同步
            vm.CurrentImagePath = target.ImagePath;
            vm.CurrentTagId = target.TagId;

            // 同步图库选中状态：仅选中新图片
            foreach (var it in items)
            {
                it.IsSelected = ReferenceEquals(it, target);
            }

            // 重置视口为适应窗口（完整可见）
            Dispatcher.UIThread.Post(() => FitToViewport(), DispatcherPriority.Background);

            UpdateArrowEnableStates();
        }

        private void OnPrevImageClick(object? sender, RoutedEventArgs e)
        {
            NavigateBy(-1);
        }

        private void OnNextImageClick(object? sender, RoutedEventArgs e)
        {
            NavigateBy(1);
        }
        // ==== Toolbar handlers ====
        // Pan tool removed per requirements
        private void OnToolPencilClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Pencil;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Pencil);
                this.Cursor = new Cursor(StandardCursorType.Cross);
                // 画笔工具同样在 RenderView 上进行
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawFreehand;
                    ApplyStyle(vm.CurrentTool);
                }
                if (_navHost != null) _navHost.IsHitTestVisible = false;
                UpdateToolButtons(_btnPencil);
            }
        }
        private void OnToolPolygonClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Polygon;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Polygon);
                this.Cursor = new Cursor(StandardCursorType.Cross);
                // 启用 RenderView 多边形模块
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawPolygon;
                    ApplyStyle(vm.CurrentTool);
                }
                if (_navHost != null) _navHost.IsHitTestVisible = false; // 绘制时禁用导航器命中，避免遮挡
                UpdateToolButtons(_btnPolygon);
            }
        }
        private void OnToolRectangleClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Rectangle;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Rectangle);
                this.Cursor = new Cursor(StandardCursorType.Cross);
                if (_navHost != null) _navHost.IsHitTestVisible = false; // 绘制时禁用导航器命中，避免遮挡
                // 启用 RenderView 矩形模块
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawRect;
                    ApplyStyle(vm.CurrentTool);
                }
                UpdateToolButtons(_btnRect);
            }
        }

        private void OnToolCircleClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Circle;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Circle);
                this.Cursor = new Cursor(StandardCursorType.Cross);
                if (_navHost != null) _navHost.IsHitTestVisible = false; // 绘制时禁用导航器命中，避免遮挡
                // 启用 RenderView 圆形模块
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawCircle;
                    ApplyStyle(vm.CurrentTool);
                }
                UpdateToolButtons(_btnCircle);
            }
        }

        private void OnToolLineClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                vm.CurrentTool = AvaloniaApplication2.Models.ToolMode.Line;
                vm.DrawingService.SetMode(AvaloniaApplication2.Models.ToolMode.Line);
                this.Cursor = new Cursor(StandardCursorType.Cross);
                if (_navHost != null) _navHost.IsHitTestVisible = false; // 绘制时禁用导航器命中，避免遮挡
                // 启用 RenderView 直线模块
                UseRenderView(true);
                if (_renderView != null)
                {
                    _renderView.Tool = ImageViewer.Drawing.ToolMode.DrawLine;
                    ApplyStyle(vm.CurrentTool);
                }
                UpdateToolButtons(_btnLine);
            }
        }

        private void OnToolCleanClick(object? sender, RoutedEventArgs e)
        {
            if (DataContext is ImageViewModel vm)
            {
                // 清除所有绘制的形状
                vm.DrawingService.ClearShapes();
                // 同步清空 RenderView 中的形状以防再次显示
                if (_renderView != null)
                {
                    _renderView.Shapes.Clear();
                }
                // 重置为默认工具（Pencil）
                OnToolPencilClick(sender, e);
                // 确保清空按钮保持未选中
                if (_btnClean != null) _btnClean.IsChecked = false;
                // 请求重绘
                if (_renderView != null)
                {
                    _renderView.InvalidateVisual();
                }
            }
        }

        // 统一使用 RenderView 进行显示与交互
        private void UseRenderView(bool _)
        {
            if (_renderView == null) return;
            _renderView.IsVisible = true;
            // 同步颜色/粗细
            if (DataContext is ImageViewModel vm)
            {
                _renderView.Stroke = vm.DrawingService.Stroke ?? Brushes.Lime;
                _renderView.StrokeThickness = vm.DrawingService.StrokeThickness;
            }
            // 确保键盘事件焦点
            _renderView.Focus();
        }

        // ==== RenderView 指针事件：用于状态栏和交互 ====
        private void OnRenderViewPointerPressed(object? sender, PointerPressedEventArgs e)
        {
            if (_renderView == null) return;
            if (DataContext is not ImageViewModel vm) return;
            var pCtrl = e.GetPosition(_renderView);
            var pImg = _renderView.MapViewToImage(pCtrl);
            var props = e.GetCurrentPoint(_renderView).Properties;
            vm.DrawingService.PointerPressed(pImg, props, e.ClickCount, props.IsRightButtonPressed);
            UpdateStatusText(pCtrl, pImg);
            e.Handled = true;
        }
        private void OnRenderViewPointerMoved(object? sender, PointerEventArgs e)
        {
            if (_renderView == null) return;
            if (DataContext is not ImageViewModel vm) return;
            var pCtrl = e.GetPosition(_renderView);
            var pImg = _renderView.MapViewToImage(pCtrl);
            var props = e.GetCurrentPoint(_renderView).Properties;
            vm.DrawingService.PointerMoved(pImg, props.IsLeftButtonPressed);

            // 光标处理现在由 RenderView 控件内部处理
            // 这里保持默认光标
            this.Cursor = new Cursor(StandardCursorType.Arrow);

            UpdateStatusText(pCtrl, pImg);
        }
        private void OnRenderViewPointerReleased(object? sender, PointerReleasedEventArgs e)
        {
            if (_renderView == null) return;
            if (DataContext is not ImageViewModel vm) return;
            var pCtrl = e.GetPosition(_renderView);
            var pImg = _renderView.MapViewToImage(pCtrl);
            vm.DrawingService.PointerReleased(pImg);
            UpdateStatusText(pCtrl, pImg);
            e.Handled = true;
        }

        // 存储最新的像素信息，用于默认状态显示
        private PixelInfoEventArgs? _lastPixelInfo;

        // RenderView 像素信息变化事件处理器
        private void OnRenderViewPixelInfoChanged(object? sender, PixelInfoEventArgs e)
        {
            _lastPixelInfo = e;
            // 检查是否需要立即更新状态文本
            if (_renderView != null)
            {
                // 如果当前不在绘制状态且没有选中形状，立即更新状态文本
                if (!_renderView.IsDrawingRect && !_renderView.IsDrawingLine && _renderView.SelectedShape == null)
                {
                    // 检查是否在绘制工具模式下但没有进行绘制
                    if (_renderView.Tool == ImageViewer.Drawing.ToolMode.DrawRect ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawLine ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawPolygon ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawCircle ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawFreehand ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.Eraser ||
                        _renderView.Tool == ImageViewer.Drawing.ToolMode.Edit)
                    {
                        UpdateDefaultStatusText();
                    }
                }
            }
        }

        // RenderView 形状选择状态变化事件处理器
        private void OnRenderViewShapeSelectionChanged(object? sender, EventArgs e)
        {
            // 当形状选择状态发生变化时，立即更新状态显示
            if (_renderView != null && DataContext is ImageViewModel vm)
            {
                // 检查是否有选中的形状，如果有则显示其信息
                var selectedShape = _renderView.SelectedShape;
                if (selectedShape != null && _renderView.Source != null && StatusText != null)
                {
                    var source = _renderView.Source;
                    double scaleX = source.PixelSize.Width / source.Size.Width;
                    double scaleY = source.PixelSize.Height / source.Size.Height;

                    switch (selectedShape)
                    {
                        case RectShape selectedRect:
                            // 显示选中矩形的信息
                            var bounds = selectedRect.Rect;
                            int x = (int)Math.Floor(bounds.Left * scaleX);
                            int y = (int)Math.Floor(bounds.Top * scaleY);
                            int w = (int)Math.Round(bounds.Width * scaleX);
                            int h = (int)Math.Round(bounds.Height * scaleY);
                            double angle = selectedRect.AngleDeg;
                            StatusText.Text = $"x={x}, y={y}, w={w}, h={h}, θ={angle:F1}°";
                            break;

                        case CircleShape selectedCircle:
                            // 显示选中椭圆的信息
                            var center = selectedCircle.Center;
                            int cx = (int)Math.Round(center.X * scaleX);
                            int cy = (int)Math.Round(center.Y * scaleY);
                            int cw = (int)Math.Round(selectedCircle.Width * scaleX);
                            int ch = (int)Math.Round(selectedCircle.Height * scaleY);
                            StatusText.Text = $"x={cx}, y={cy}, w={cw}, h={ch}";
                            break;

                        case LineShape selectedLine:
                            // 显示选中直线的信息
                            var start = selectedLine.Start;
                            var end = selectedLine.End;
                            var dx = end.X - start.X;
                            var dy = end.Y - start.Y;

                            // 计算像素坐标下的长度
                            double lengthPixels = Math.Sqrt((dx * scaleX) * (dx * scaleX) + (dy * scaleY) * (dy * scaleY));

                            // 计算角度（相对于水平线，逆时针为正）
                            double angleRad = Math.Atan2(dy, dx);
                            double angleDeg = angleRad * 180.0 / Math.PI;
                            if (angleDeg < 0) angleDeg += 360;

                            StatusText.Text = $"length={lengthPixels:F1}, θ={angleDeg:F1}°";
                            break;

                        default:
                            // 其他形状类型，显示默认信息
                            UpdateDefaultStatusText();
                            break;
                    }
                }
                else
                {
                    // 没有选中形状，显示默认信息
                    UpdateDefaultStatusText();
                }
            }
        }

        // 更新左下角状态文本：仿照ImageJ的显示逻辑
        private void UpdateStatusText(Point pRaw, Point p)
        {
            if (StatusText == null || _renderView == null) return;
            if (DataContext is not ImageViewModel vm) return;

            // 优先检查绘制状态，仿照ImageJ逻辑
            if (_renderView.IsDrawingRect && _renderView.ActiveRect != null && _renderView.Source != null)
            {
                // 矩形绘制状态：显示左上角坐标、宽度、高度和角度
                var rect = _renderView.ActiveRect;
                var source = _renderView.Source;

                // 将图像坐标映射为像素坐标
                double scaleX = source.PixelSize.Width / source.Size.Width;
                double scaleY = source.PixelSize.Height / source.Size.Height;

                var bounds = rect.Rect;
                int x = (int)Math.Floor(bounds.Left * scaleX);
                int y = (int)Math.Floor(bounds.Top * scaleY);
                int w = (int)Math.Round(bounds.Width * scaleX);
                int h = (int)Math.Round(bounds.Height * scaleY);
                double angle = rect.AngleDeg;

                StatusText.Text = $"x={x}, y={y}, w={w}, h={h}, θ={angle:F1}°";
                return;
            }

            if (_renderView.IsDrawingLine && _renderView.ActiveLine != null && _renderView.Source != null)
            {
                // 直线绘制状态：显示当前光标坐标、长度和角度
                var line = _renderView.ActiveLine;
                var source = _renderView.Source;

                var start = line.Start;
                var end = line.End;
                var dx = end.X - start.X;
                var dy = end.Y - start.Y;

                // 将图像坐标映射为像素坐标
                double scaleX = source.PixelSize.Width / source.Size.Width;
                double scaleY = source.PixelSize.Height / source.Size.Height;

                // 当前光标位置（终点）的像素坐标
                int currentX = (int)Math.Round(end.X * scaleX);
                int currentY = (int)Math.Round(end.Y * scaleY);

                // 计算长度
                double lengthPixels = Math.Sqrt((dx * scaleX) * (dx * scaleX) + (dy * scaleY) * (dy * scaleY));

                // 计算角度（相对于水平线，逆时针为正）
                double angleRad = Math.Atan2(dy, dx);
                double angleDeg = angleRad * 180.0 / Math.PI;

                // 确保角度在0-360度范围内
                if (angleDeg < 0) angleDeg += 360;

                StatusText.Text = $"x={currentX}, y={currentY}, length={lengthPixels:F1}, θ={angleDeg:F1}°";
                return;
            }

            // 检查是否有选中的RenderView形状（优先于工具模式检查）
            var selectedShape = _renderView.SelectedShape;
            if (selectedShape != null && _renderView.Source != null)
            {
                var source = _renderView.Source;
                double scaleX = source.PixelSize.Width / source.Size.Width;
                double scaleY = source.PixelSize.Height / source.Size.Height;

                switch (selectedShape)
                {
                    case RectShape selectedRect:
                        // 显示选中矩形的实时信息：左上角坐标、宽度、高度和旋转角度
                        var bounds = selectedRect.Rect;
                        int x = (int)Math.Floor(bounds.Left * scaleX);
                        int y = (int)Math.Floor(bounds.Top * scaleY);
                        int w = (int)Math.Round(bounds.Width * scaleX);
                        int h = (int)Math.Round(bounds.Height * scaleY);
                        double angle = selectedRect.AngleDeg;
                        StatusText.Text = $"x={x}, y={y}, w={w}, h={h}, θ={angle:F1}°";
                        return;

                    case CircleShape selectedCircle:
                        // 显示选中椭圆的实时信息：中心点坐标、宽度和高度
                        var center = selectedCircle.Center;
                        int cx = (int)Math.Round(center.X * scaleX);
                        int cy = (int)Math.Round(center.Y * scaleY);
                        int cw = (int)Math.Round(selectedCircle.Width * scaleX);
                        int ch = (int)Math.Round(selectedCircle.Height * scaleY);
                        StatusText.Text = $"x={cx}, y={cy}, w={cw}, h={ch}";
                        return;

                    case LineShape selectedLine:
                        // 显示选中直线的实时信息：长度和角度
                        var start = selectedLine.Start;
                        var end = selectedLine.End;
                        var dx = end.X - start.X;
                        var dy = end.Y - start.Y;

                        // 计算像素坐标下的长度
                        double lengthPixels = Math.Sqrt((dx * scaleX) * (dx * scaleX) + (dy * scaleY) * (dy * scaleY));

                        // 计算角度（相对于水平线，逆时针为正）
                        double angleRad = Math.Atan2(dy, dx);
                        double angleDeg = angleRad * 180.0 / Math.PI;
                        if (angleDeg < 0) angleDeg += 360;

                        StatusText.Text = $"length={lengthPixels:F1}, θ={angleDeg:F1}°";
                        return;
                }
            }

            // 检查绘制工具模式但没有正在绘制的情况：显示当前光标坐标和像素值
            if (_renderView.Tool == ImageViewer.Drawing.ToolMode.DrawRect && !_renderView.IsDrawingRect)
            {
                // 在矩形绘制工具模式下，当没有进行绘制时，显示当前光标的坐标和像素值
                UpdateDefaultStatusText();
                return;
            }

            if (_renderView.Tool == ImageViewer.Drawing.ToolMode.DrawLine && !_renderView.IsDrawingLine)
            {
                // 在直线绘制工具模式下，当没有进行绘制时，显示当前光标的坐标和像素值
                UpdateDefaultStatusText();
                return;
            }

            if ((_renderView.Tool == ImageViewer.Drawing.ToolMode.DrawPolygon ||
                 _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawCircle ||
                 _renderView.Tool == ImageViewer.Drawing.ToolMode.DrawFreehand ||
                 _renderView.Tool == ImageViewer.Drawing.ToolMode.Eraser) &&
                !_renderView.IsDrawingRect && !_renderView.IsDrawingLine)
            {
                // 在其他绘制工具模式下，当没有进行绘制时，显示当前光标的坐标和像素值
                UpdateDefaultStatusText();
                return;
            }

            // 旧的 ROI 状态显示已移除，现在由 RenderView 控件处理
            // 移除 RoiRectangle 的显示，因为它显示的是 RECT(0,0) 这样的内容

            // 默认状态：显示鼠标坐标和RGB值
            UpdateDefaultStatusText();
        }

        // 更新默认状态的状态文本（坐标和RGB值）
        private void UpdateDefaultStatusText()
        {
            if (StatusText == null || _lastPixelInfo == null) return;

            if (_lastPixelInfo.InBounds)
            {
                if (_lastPixelInfo.PixelDataAvailable)
                {
                    StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}, value={_lastPixelInfo.R:000},{_lastPixelInfo.G:000},{_lastPixelInfo.B:000}";
                }
                else
                {
                    // 像素数据不可用（如 RenderTargetBitmap），只显示坐标
                    StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}";
                }
            }
            else
            {
                // 光标移出图像范围时，不显示任何坐标信息，仿照ImageJ的行为
                StatusText.Text = "";
            }
        }

        private void UpdateRoiCanvasSize()
        {
            if (_roiCanvas == null || _mainImage == null) return;
            // 优先使用位图像素尺寸以避免偏移；退化为可见 Bounds
            if (_mainImage.Source is Bitmap bmp)
            {
                _roiCanvas.Width = bmp.PixelSize.Width;
                _roiCanvas.Height = bmp.PixelSize.Height;
            }
            else
            {
                _roiCanvas.Width = _mainImage.Bounds.Width;
                _roiCanvas.Height = _mainImage.Bounds.Height;
            }
            // 更新图像边界供工具进行夹制
            if (DataContext is ImageViewModel vm)
            {
                vm.DrawingService.SetImageBounds(new Rect(0, 0, _roiCanvas.Width, _roiCanvas.Height));
            }
        }

        // RedrawRois 方法已移除，ROI 渲染现在由 RenderView 控件处理

        // ==== Toolbar option handlers ====
        private void OnColorSelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (DataContext is not ImageViewModel vm) return;
            if (sender is ComboBox cb && cb.SelectedItem is ComboBoxItem item)
            {
                var tag = item.Tag?.ToString() ?? "Lime";
                vm.DrawingService.Stroke = Brush.Parse(tag);
                if (_renderView != null)
                {
                    _renderView.Stroke = vm.DrawingService.Stroke;
                    _renderView.InvalidateVisual();
                }
            }
        }

        private void OnThicknessChanged(object? sender, RangeBaseValueChangedEventArgs e)
        {
            if (DataContext is not ImageViewModel vm) return;
            vm.DrawingService.StrokeThickness = e.NewValue;
            if (_renderView != null)
            {
                _renderView.StrokeThickness = vm.DrawingService.StrokeThickness;
                _renderView.InvalidateVisual();
            }
        }

        private async void OnApplyEditsClick(object? sender, RoutedEventArgs e)
        {
            if (_mainImage == null || _roiCanvas == null) return;
            if (_mainImage.Source is not Bitmap baseBmp) return;
            if (DataContext is not ImageViewModel vm) return;

            // Build a visual for composite
            var root = new Grid();
            var img = new Image { Source = baseBmp, Stretch = Stretch.None };
            var overlay = new Canvas { Width = _roiCanvas.Width, Height = _roiCanvas.Height, Background = Brushes.Transparent };

            // Copy current ROI visuals
            foreach (var child in _roiCanvas.Children)
            {
                if (child is Rectangle r)
                {
                    var clone = new Rectangle
                    {
                        Width = r.Width,
                        Height = r.Height,
                        Stroke = r.Stroke,
                        StrokeThickness = r.StrokeThickness,
                        StrokeDashArray = r.StrokeDashArray
                    };
                    Canvas.SetLeft(clone, Canvas.GetLeft(r));
                    Canvas.SetTop(clone, Canvas.GetTop(r));
                    overlay.Children.Add(clone);
                }
                else if (child is Polyline p)
                {
                    var clone = new Polyline
                    {
                        Points = new Avalonia.Collections.AvaloniaList<Point>(p.Points),
                        Stroke = p.Stroke,
                        StrokeThickness = p.StrokeThickness,
                        StrokeDashArray = p.StrokeDashArray
                    };
                    overlay.Children.Add(clone);
                }
                else if (child is Polygon pg)
                {
                    var clone = new Polygon
                    {
                        Points = new Avalonia.Collections.AvaloniaList<Point>(pg.Points),
                        Stroke = pg.Stroke,
                        StrokeThickness = pg.StrokeThickness,
                        StrokeDashArray = pg.StrokeDashArray,
                        Fill = pg.Fill
                    };
                    overlay.Children.Add(clone);
                }
            }

            root.Children.Add(img);
            root.Children.Add(overlay);

            root.Measure(new Size(baseBmp.PixelSize.Width, baseBmp.PixelSize.Height));
            root.Arrange(new Rect(0, 0, baseBmp.PixelSize.Width, baseBmp.PixelSize.Height));

            var rtb = new RenderTargetBitmap(new PixelSize(baseBmp.PixelSize.Width, baseBmp.PixelSize.Height));
            rtb.Render(root);

            // Save composite next to original as display image
            var dir = System.IO.Path.GetDirectoryName(vm.CurrentImagePath) ?? Environment.CurrentDirectory;
            var name = System.IO.Path.GetFileNameWithoutExtension(vm.CurrentImagePath);
            var outPath = System.IO.Path.Combine(dir, $"{name}_display.png");
            await using (var fs = File.Open(outPath, FileMode.Create, FileAccess.Write))
            {
                rtb.Save(fs);
            }

            // Update display paths for both image view and gallery item
            var gallery = vm.GalleryRef;
            if (gallery?.CurrentItem != null)
            {
                gallery.CurrentItem.DisplayPath = outPath;
            }
            vm.DisplayImagePath = outPath;

            // Redraw to reflect possibly different resolution
            UpdateRoiCanvasSize();
            // ROI 重绘现在由 RenderView 控件处理
        }

        private void OnPrevPointerPressed(object? sender, PointerPressedEventArgs e)
        {
            NavigateBy(-1);
            _navRepeatDelta = -1;
            _navRepeatTimer?.Start();
        }

        private void OnNextPointerPressed(object? sender, PointerPressedEventArgs e)
        {
            NavigateBy(1);
            _navRepeatDelta = 1;
            _navRepeatTimer?.Start();
        }

        private void OnNavPointerReleased(object? sender, PointerReleasedEventArgs e)
        {
            _navRepeatTimer?.Stop();
        }

        private void OnNavPointerExited(object? sender, PointerEventArgs e)
        {
            _navRepeatTimer?.Stop();
        }

        private void OnRootKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Left)
            {
                NavigateBy(-1);
                e.Handled = true;
            }
            else if (e.Key == Key.Right)
            {
                NavigateBy(1);
                e.Handled = true;
            }
            else if (e.Key == Key.Delete)
            {
                if (DataContext is ImageViewModel vm)
                {
                    vm.DrawingService.DeleteSelected();
                    // ROI 重绘现在由 RenderView 控件处理
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Z && (e.KeyModifiers & KeyModifiers.Control) == KeyModifiers.Control)
            {
                if (DataContext is ImageViewModel vm)
                {
                    vm.DrawingService.Undo();
                    // ROI 重绘现在由 RenderView 控件处理
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Escape)
            {
                if (DataContext is ImageViewModel vm)
                {
                    vm.DrawingService.CancelActive();
                    e.Handled = true;
                }
            }
        }

        // ===== Tag popup handlers (match Gallery behavior) =====
        private void OnTagAddButtonClick(object? sender, RoutedEventArgs e)
        {
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null)
            {
                popup.Placement = PlacementMode.Right;
                popup.IsOpen = !popup.IsOpen;
            }
        }

        private void OnTagAddCancelClick(object? sender, RoutedEventArgs e)
        {
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null) popup.IsOpen = false;
        }

        private void OnTagCreateClick(object? sender, RoutedEventArgs e)
        {
            // Button bound to AddTagCommand; here just close popup
            var popup = this.FindControl<Popup>("PopupAddTag");
            if (popup != null) popup.IsOpen = false;
        }

        private void OnNewTagNameKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key != Key.Enter) return;
            if (DataContext is ImageViewModel vm && vm.AddTagCommand?.CanExecute(null) == true)
            {
                vm.AddTagCommand.Execute(null);
                var popup = this.FindControl<Popup>("PopupAddTag");
                if (popup != null) popup.IsOpen = false;
                e.Handled = true;
            }
        }

        // RenderView滚轮事件处理器 - 更新导航器
        private void OnRenderViewWheelChanged(object? sender, PointerWheelEventArgs e)
        {
            // 延迟更新导航器，避免滚轮操作时的卡顿
            Dispatcher.UIThread.Post(() =>
            {
                UpdateViewportValues();
                UpdateNavigatorRect();
            }, DispatcherPriority.Background);
        }
    }
}
