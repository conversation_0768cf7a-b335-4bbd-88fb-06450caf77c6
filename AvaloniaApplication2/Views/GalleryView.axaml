<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:AvaloniaApplication2.ViewModels"
             xmlns:iconPacks="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:conv="using:AvaloniaApplication2.Converters"
             xmlns:avconv="using:Avalonia.Data.Converters"
             mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="720"
             x:Class="AvaloniaApplication2.Views.GalleryView"
             x:DataType="vm:GalleryViewModel"
             x:Name="Root">

    <UserControl.Resources>
        <conv:PathToBitmapConverter x:Key="PathToBitmapConverter"/>
        <vm:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <vm:FilenamePreviewConverter x:Key="FilenamePreviewConverter"/>
        <conv:PreferDisplayBitmapConverter x:Key="PreferDisplayBitmapConverter"/>
    </UserControl.Resources>

    <Grid ColumnDefinitions="300, *, 50" RowDefinitions="*,Auto" Background="#1E1E1E">

        <!-- Left Sidebar (scrollable when content overflows) -->
        <Border Grid.Row="0" Grid.Column="0" Background="#2E2E2E" Padding="15">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled">
                <StackPanel Spacing="15">
                    <!-- Project Name and Type -->
                    <Border Background="#3C3C3C" CornerRadius="5" Padding="10">
                        <Grid ColumnDefinitions="Auto, *" VerticalAlignment="Center">
                            <iconPacks:PackIconMaterial Kind="Tag" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="star_seg" Foreground="White" FontSize="16" FontWeight="Bold"/>
                                <TextBlock Text="语义分割" Foreground="LightGray" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Image Details -->
                    <StackPanel>
                        <Grid ColumnDefinitions="*, Auto, Auto">
                            <TextBlock Text="图像" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                            <Button Grid.Column="1" Background="Transparent" BorderBrush="Transparent" Click="OnImportFolderClick">
                                <iconPacks:PackIconMaterial Kind="FolderOutline" Foreground="White"/>
                            </Button>
                            <Button Grid.Column="2" Background="Transparent" BorderBrush="Transparent" Click="OnImportSingleClick">
                                <iconPacks:PackIconMaterial Kind="ImagePlus" Foreground="White"/>
                            </Button>
                        </Grid>
                        <Border BorderBrush="Gray" BorderThickness="0,0,0,1" Padding="0,5" Margin="0,5,0,10">
                            <StackPanel>
                                <TextBlock Text="{Binding ImageCountText}" Foreground="LightGray"/>
                                <TextBlock Text="{Binding CurrentName, StringFormat=名称: {0}}" Foreground="White" Margin="0,5,0,0"/>
                                <TextBlock Text="{Binding CurrentPath, StringFormat=路径: {0}}" Foreground="White" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- Selection Info -->
                    <StackPanel>
                        <Grid ColumnDefinitions="*, Auto, Auto">
                            <TextBlock Text="已选择" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                            <Button Grid.Column="1" Background="Transparent" BorderBrush="Transparent">
                                <iconPacks:PackIconMaterial Kind="Close" Foreground="White"/>
                            </Button>
                            <Button Grid.Column="2" Background="Transparent" BorderBrush="Transparent">
                                <iconPacks:PackIconMaterial Kind="TrashCanOutline" Foreground="White"/>
                            </Button>
                        </Grid>
                        <Border BorderBrush="Gray" BorderThickness="0,0,0,1" Padding="0,5" Margin="0,5,0,10">
                            <TextBlock Text="{Binding SelectedSummaryText}" Foreground="LightGray"/>
                        </Border>
                    </StackPanel>

                    <!-- Tag Categories -->
                    <StackPanel>
                        <Grid ColumnDefinitions="*,Auto" VerticalAlignment="Center">
                            <TextBlock Text="标签类别" Foreground="White" FontWeight="Bold"/>
                            <Button Grid.Column="1" Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="4" Padding="6,2" Click="OnTagAddButtonClick">
                                <iconPacks:PackIconMaterial Kind="Plus" Foreground="White"/>
                            </Button>
                        </Grid>

                        <TextBlock Text="为所选图像设置类别:" Foreground="LightGray" Margin="0,6,0,4"/>

                        <!-- Tag list -->
                        <ItemsControl ItemsSource="{Binding Tags}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button x:Name="TagItemButton"
                                            Command="{Binding DataContext.AssignTagToSelectedCommand, ElementName=Root}"
                                            CommandParameter="{Binding}"
                                            Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="6" Margin="0,4,0,0" Padding="8,6" HorizontalAlignment="Stretch">
                                        <Grid ColumnDefinitions="Auto,Auto,*,Auto">
                                            <!-- Icon with color (first) -->
                                            <iconPacks:PackIconMaterial Grid.Column="0" Kind="{Binding IconKind}" Foreground="{Binding Color}" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <!-- Colored id badge -->
                                            <Border Grid.Column="1" Background="{Binding Color}" CornerRadius="4" Padding="6,0" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Id}" Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <!-- Name -->
                                            <TextBlock Grid.Column="2" Text="{Binding Name}" Foreground="White" VerticalAlignment="Center" Margin="8,0,0,0"/>
                                            <!-- Hover delete button -->
                                            <Button Grid.Column="3"
                                                    Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="4" Padding="2" Margin="6,0,0,0"
                                                    IsVisible="{Binding IsPointerOver, ElementName=TagItemButton}"
                                                    Command="{Binding DataContext.DeleteTagCommand, ElementName=Root}"
                                                    CommandParameter="{Binding}">
                                                <iconPacks:PackIconMaterial Kind="TrashCanOutline" Foreground="White" Width="16" Height="16"/>
                                            </Button>
                                        </Grid>
                                    </Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <!-- Add Tag Popup -->
                        <Popup x:Name="PopupAddTag" Placement="Right">
                            <Border Background="#2E2E2E" Padding="10" CornerRadius="6" BorderBrush="#4A4A4A" BorderThickness="1">
                                <StackPanel Spacing="8" MinWidth="260">
                                    <TextBlock Text="新建标签" Foreground="White" FontWeight="Bold"/>
                                    <StackPanel>
                                        <TextBlock Text="名称" Foreground="LightGray"/>
                                        <TextBox Text="{Binding NewTagName, Mode=TwoWay}" KeyDown="OnNewTagNameKeyDown"/>
                                    </StackPanel>
                                    <StackPanel>
                                        <TextBlock Text="颜色" Foreground="LightGray"/>
                                        <ComboBox ItemsSource="{Binding PresetColors}" SelectedItem="{Binding NewTagColor, Mode=TwoWay}">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" Spacing="8">
                                                        <Border Width="16" Height="16" CornerRadius="3" Background="{Binding}"/>
                                                        <TextBlock Text="{Binding}" Foreground="White"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>
                                    </StackPanel>
                                    <!-- Status selector: OK / NG (mutually exclusive) -->
                                    <StackPanel>
                                        <TextBlock Text="状态" Foreground="LightGray"/>
                                        <StackPanel Orientation="Horizontal" Spacing="8">
                                            <RadioButton GroupName="NewTagStatusGroup" IsChecked="{Binding NewTagIsOk, Mode=TwoWay}" Padding="10,6" MinWidth="80">
                                                <StackPanel Orientation="Horizontal" Spacing="6">
                                                    <iconPacks:PackIconMaterial Kind="ThumbUp"/>
                                                    <TextBlock Text="良好"/>
                                                </StackPanel>
                                            </RadioButton>
                                            <RadioButton GroupName="NewTagStatusGroup" IsChecked="{Binding NewTagIsOk, Converter={x:Static conv:BooleanNegationConverter.Instance}, Mode=TwoWay}" Padding="10,6" MinWidth="80">
                                                <StackPanel Orientation="Horizontal" Spacing="6">
                                                    <iconPacks:PackIconMaterial Kind="ThumbDown"/>
                                                    <TextBlock Text="异常"/>
                                                </StackPanel>
                                            </RadioButton>
                                        </StackPanel>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Spacing="8" HorizontalAlignment="Right">
                                        <Button Content="取消" Click="OnTagAddCancelClick"/>
                                        <Button Content="创建" Command="{Binding AddTagCommand}" Click="OnTagCreateClick"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Popup>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Main Content (Image Grid) -->
        <ScrollViewer x:Name="GalleryScroll" Grid.Row="0" Grid.Column="1" Background="#1E1E1E"
                      ScrollChanged="OnGalleryScrollChanged">
            <ItemsControl x:Name="GalleryItemsControl" ItemsSource="{Binding GalleryItems}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <!-- Left-aligned wrap to avoid uneven spacing across rows -->
                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <!-- Outer tile: exact width equals ThumbnailSize for perfect column alignment -->
                        <Border Background="Transparent" CornerRadius="6" Margin="5" Padding="0"
                                Width="{Binding ElementName=Root, Path=DataContext.ThumbnailSize}"
                                PointerPressed="OnItemPointerPressed"
                                DoubleTapped="OnItemDoubleTapped">
                            <Grid RowDefinitions="Auto,Auto">
                                <!-- Fixed square thumbnail area -->
                                <Border Width="{Binding ElementName=Root, Path=DataContext.ThumbnailSize}"
                                        Height="{Binding ElementName=Root, Path=DataContext.ThumbnailSize}"
                                        Background="Transparent" CornerRadius="8" ClipToBounds="True">
                                    <Grid>
                                        <Image Stretch="UniformToFill" HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <Image.Source>
                                                <MultiBinding Converter="{StaticResource PreferDisplayBitmapConverter}">
                                                    <Binding Path="ThumbnailPath"/>
                                                    <Binding Path="DisplayPath"/>
                                                    <Binding Path="ImagePath"/>
                                                </MultiBinding>
                                            </Image.Source>
                                        </Image>

                                        <!-- Loading indicator for thumbnail generation -->
                                        <Border IsVisible="{Binding IsLoadingThumbnail}"
                                                Background="#80000000" CornerRadius="8">
                                            <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <Ellipse Width="24" Height="24"
                                                         Stroke="#FFFFFF" StrokeThickness="2"
                                                         StrokeDashArray="4,2">
                                                    <Ellipse.RenderTransform>
                                                        <RotateTransform/>
                                                    </Ellipse.RenderTransform>
                                                    <Ellipse.Styles>
                                                        <Style Selector="Ellipse">
                                                            <Style.Animations>
                                                                <Animation Duration="0:0:1" IterationCount="Infinite">
                                                                    <KeyFrame Cue="0%">
                                                                        <Setter Property="RenderTransform.Angle" Value="0"/>
                                                                    </KeyFrame>
                                                                    <KeyFrame Cue="100%">
                                                                        <Setter Property="RenderTransform.Angle" Value="360"/>
                                                                    </KeyFrame>
                                                                </Animation>
                                                            </Style.Animations>
                                                        </Style>
                                                    </Ellipse.Styles>
                                                </Ellipse>
                                            </Grid>
                                        </Border>
                                        <!-- Selected overlay (底纹) -->
                                        <Border IsVisible="{Binding IsSelected}"
                                                Background="#2000E0B8" CornerRadius="8"/>
                                        <!-- Selected stroke wrapping image only -->
                                        <Border IsVisible="{Binding IsSelected}"
                                                BorderBrush="#00E0B8" BorderThickness="2" CornerRadius="8"/>
                                        <!-- Top-right tag badge (single tag per image) -->
                                        <Border
                                            IsVisible="{Binding TagId, Converter={x:Static conv:IntGreaterThanZeroToBoolConverter.Instance}}"
                                            HorizontalAlignment="Right" VerticalAlignment="Top" Margin="4"
                                            CornerRadius="6" Padding="4,3">
                                            <Border.Background>
                                                <MultiBinding Converter="{x:Static conv:TagIdToColorConverter.Instance}">
                                                    <Binding Path="TagId"/>
                                                    <Binding Path="DataContext.Tags" ElementName="Root"/>
                                                </MultiBinding>
                                            </Border.Background>
                                            <iconPacks:PackIconMaterial Foreground="White" Width="16" Height="16">
                                                <iconPacks:PackIconMaterial.Kind>
                                                    <MultiBinding Converter="{x:Static conv:TagIdToIconKindConverter.Instance}">
                                                        <Binding Path="TagId"/>
                                                        <Binding Path="DataContext.Tags" ElementName="Root"/>
                                                    </MultiBinding>
                                                </iconPacks:PackIconMaterial.Kind>
                                            </iconPacks:PackIconMaterial>
                                        </Border>
                                        <!-- Top-left selection toggle, subtle appearance -->
                                        <Button Background="Transparent" BorderThickness="0" CornerRadius="10"
                                                Padding="0" Width="16" Height="16" Margin="4"
                                                HorizontalAlignment="Left" VerticalAlignment="Top"
                                                Click="OnToggleItemSelectClick">
                                            <Grid>
                                                <!-- Selected: green check circle only to avoid double icons -->
                                                <iconPacks:PackIconMaterial Kind="CheckCircle" Width="14" Height="14"
                                Foreground="#9BEF00" IsVisible="{Binding IsSelected}"/>
                                            </Grid>
                                        </Button>
                                    </Grid>
                                </Border>
                                <!-- Filename: show prefix with extension preserved. Set max prefix via ConverterParameter. -->
                                <TextBlock Grid.Row="1"
                                           Width="{Binding ElementName=Root, Path=DataContext.ThumbnailSize}"
                                           Text="{Binding Name, Converter={StaticResource FilenamePreviewConverter}, ConverterParameter=12}"
                                           Foreground="White"
                                           Margin="0,6,0,0"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           TextTrimming="CharacterEllipsis"
                                           TextWrapping="NoWrap"/>
							</Grid>
						</Border>
					</DataTemplate>
				</ItemsControl.ItemTemplate>
			</ItemsControl>
		</ScrollViewer>

        <!-- Bottom slim annotation progress bar (full width) -->
        <Border Grid.Row="1" Grid.ColumnSpan="3"
                Background="#2E2E2E" BorderBrush="#3A3A3A" BorderThickness="0,1,0,0"
                IsVisible="{Binding TotalCount, Converter={x:Static conv:IntGreaterThanZeroToBoolConverter.Instance}}"
                Padding="8,4">
            <Grid ColumnDefinitions="*,Auto" VerticalAlignment="Center">
                <!-- Thin progress bar across -->
                <ProgressBar Grid.Column="0" Minimum="0" Maximum="100" Value="{Binding AnnotationProgressPercent}"
                             Height="3" CornerRadius="1.5" />
                <!-- Percentage text on the right -->
                <TextBlock Grid.Column="1" Text="{Binding AnnotationProgressPercent, StringFormat=已标注：\{0\}%}"
                           Foreground="#9BEF00" FontSize="12" Margin="10,0,0,0" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Right Toolbar -->
        <Border Grid.Row="0" Grid.Column="2" Background="#2E2E2E" Padding="5">
            <StackPanel Spacing="15" HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,10,0,0">
				<Button Classes="ToolbarButton" ToolTip.Tip="移除图像" Click="OnRemoveSelectedClick">
					<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.TrashCanOutline}" />
				</Button>
				<!-- Thumbnail size with popup slider -->
				<Grid>
					<Button x:Name="BtnSize" Classes="ToolbarButton" ToolTip.Tip="调整缩略图大小" Click="OnSizeButtonClick">
						<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.ViewGridPlusOutline}" />
					</Button>
					<Popup x:Name="PopupSize" PlacementTarget="{Binding ElementName=BtnSize}" Placement="Left" Opened="OnSizePopupOpened">
						<Border Background="#2E2E2E" Padding="8" CornerRadius="6">
							<StackPanel Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
								<StackPanel Spacing="4" HorizontalAlignment="Center">
									<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.ViewGridOutline}" Foreground="#9BEF00"/>
									<Button Content="复位" FontSize="11" Padding="4,2" Margin="0,2,0,0" Click="OnSizeResetClick"/>
								</StackPanel>
								<Slider x:Name="SliderSize"
                                        Minimum="96" Maximum="220" SmallChange="2" LargeChange="10"
                                        Value="{Binding ElementName=Root, Path=DataContext.ThumbnailSize, Mode=TwoWay}"
                                        PropertyChanged="OnSizeSliderPropertyChanged" Width="180"/>
							</StackPanel>
						</Border>
					</Popup>
				</Grid>
				<!-- Brightness with popup slider -->
				<Grid>
					<Button x:Name="BtnBrightness" Classes="ToolbarButton" ToolTip.Tip="调整亮度" Click="OnBrightnessButtonClick">
						<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.Brightness5}" />
					</Button>
					<Popup x:Name="PopupBrightness" PlacementTarget="{Binding ElementName=BtnBrightness}" Placement="Left" Opened="OnBrightnessPopupOpened" Closed="OnBrightnessPopupClosed">
						<Border Background="#2E2E2E" Padding="8" CornerRadius="6">
							<StackPanel Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
								<StackPanel Spacing="4" HorizontalAlignment="Center">
									<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.Brightness5}" Foreground="#9BEF00"/>
									<Button Content="复位" FontSize="11" Padding="4,2" Margin="0,2,0,0" Click="OnBrightnessResetClick"/>
								</StackPanel>
								<Slider x:Name="SliderBrightness" Minimum="0.5" Maximum="1.5" SmallChange="0.05" LargeChange="0.1" Value="1.0" Width="180" PropertyChanged="OnBrightnessSliderPropertyChanged"/>
							</StackPanel>
						</Border>
					</Popup>
				</Grid>
				<!-- Contrast with popup slider -->
				<Grid>
					<Button x:Name="BtnContrast" Classes="ToolbarButton" ToolTip.Tip="调整对比度" Click="OnContrastButtonClick">
						<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.ContrastCircle}" />
					</Button>
					<Popup x:Name="PopupContrast" PlacementTarget="{Binding ElementName=BtnContrast}" Placement="Left" Opened="OnContrastPopupOpened" Closed="OnContrastPopupClosed">
						<Border Background="#2E2E2E" Padding="8" CornerRadius="6">
							<StackPanel Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
								<StackPanel Spacing="4" HorizontalAlignment="Center">
									<iconPacks:PackIconMaterial Kind="{x:Static iconPacks:PackIconMaterialKind.ContrastCircle}" Foreground="#9BEF00"/>
									<Button Content="复位" FontSize="11" Padding="4,2" Margin="0,2,0,0" Click="OnContrastResetClick"/>
								</StackPanel>
								<Slider x:Name="SliderContrast" Minimum="0.5" Maximum="1.5" SmallChange="0.05" LargeChange="0.1" Value="1.0" Width="180" PropertyChanged="OnContrastSliderPropertyChanged"/>
							</StackPanel>
						</Border>
					</Popup>
				</Grid>
			</StackPanel>
		</Border>

	</Grid>
</UserControl>