using System;
using System.Collections.Concurrent;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Media.Imaging;
using OpenCvSharp;

namespace AvaloniaApplication2.Services
{
    public interface IThumbnailService
    {
        Task<string?> GetThumbnailAsync(string imagePath, int size, CancellationToken cancellationToken = default);
        void ClearCache();
        Task PreloadThumbnailsAsync(IEnumerable<string> imagePaths, int size, CancellationToken cancellationToken = default);
    }

    public class ThumbnailService : IThumbnailService
    {
        private readonly string _cacheDirectory;
        private readonly ConcurrentDictionary<string, Task<string?>> _pendingTasks = new();
        private readonly SemaphoreSlim _semaphore = new(Environment.ProcessorCount); // 限制并发数

        public ThumbnailService()
        {
            _cacheDirectory = Path.Combine(Path.GetTempPath(), "AvaloniaApp_Thumbnails");
            Directory.CreateDirectory(_cacheDirectory);
        }

        public async Task<string?> GetThumbnailAsync(string imagePath, int size, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(imagePath) || !File.Exists(imagePath))
                return null;

            var cacheKey = GenerateCacheKey(imagePath, size);
            var cachePath = Path.Combine(_cacheDirectory, cacheKey + ".jpg");

            // 检查缓存
            if (File.Exists(cachePath))
            {
                try
                {
                    // 验证缓存文件是否比原文件新
                    var cacheTime = File.GetLastWriteTime(cachePath);
                    var imageTime = File.GetLastWriteTime(imagePath);
                    if (cacheTime >= imageTime)
                        return cachePath;
                }
                catch
                {
                    // 忽略错误，重新生成缩略图
                }
            }

            // 避免重复生成同一个缩略图
            var taskKey = $"{imagePath}_{size}";
            if (_pendingTasks.TryGetValue(taskKey, out var existingTask))
                return await existingTask;

            var task = GenerateThumbnailAsync(imagePath, cachePath, size, cancellationToken);
            _pendingTasks[taskKey] = task;

            try
            {
                return await task;
            }
            finally
            {
                _pendingTasks.TryRemove(taskKey, out _);
            }
        }

        private async Task<string?> GenerateThumbnailAsync(string imagePath, string cachePath, int size, CancellationToken cancellationToken)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                return await Task.Run(() =>
                {
                    try
                    {
                        using var src = Cv2.ImRead(imagePath, ImreadModes.Color);
                        if (src.Empty()) return null;

                        // 计算缩略图尺寸，保持宽高比
                        var (width, height) = CalculateThumbnailSize(src.Width, src.Height, size);
                        
                        using var resized = new Mat();
                        Cv2.Resize(src, resized, new OpenCvSharp.Size(width, height), interpolation: InterpolationFlags.Area);

                        // 保存为JPEG格式以减小文件大小
                        var encodeParams = new int[] { (int)ImwriteFlags.JpegQuality, 85 };
                        Cv2.ImWrite(cachePath, resized, encodeParams);

                        return cachePath;
                    }
                    catch
                    {
                        return null;
                    }
                }, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private static (int width, int height) CalculateThumbnailSize(int originalWidth, int originalHeight, int maxSize)
        {
            if (originalWidth <= maxSize && originalHeight <= maxSize)
                return (originalWidth, originalHeight);

            var ratio = Math.Min((double)maxSize / originalWidth, (double)maxSize / originalHeight);
            return ((int)(originalWidth * ratio), (int)(originalHeight * ratio));
        }

        private static string GenerateCacheKey(string imagePath, int size)
        {
            var input = $"{imagePath}_{size}_{File.GetLastWriteTime(imagePath):yyyyMMddHHmmss}";
            using var sha1 = SHA1.Create();
            var hash = sha1.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToHexString(hash);
        }

        public void ClearCache()
        {
            try
            {
                if (Directory.Exists(_cacheDirectory))
                {
                    foreach (var file in Directory.GetFiles(_cacheDirectory))
                    {
                        try { File.Delete(file); } catch { }
                    }
                }
            }
            catch { }
        }

        public async Task PreloadThumbnailsAsync(IEnumerable<string> imagePaths, int size, CancellationToken cancellationToken = default)
        {
            var tasks = imagePaths
                .Where(path => !string.IsNullOrWhiteSpace(path) && File.Exists(path))
                .Select(path => GetThumbnailAsync(path, size, cancellationToken))
                .ToArray();

            try
            {
                await Task.WhenAll(tasks);
            }
            catch (OperationCanceledException)
            {
                // 预加载被取消是正常的
            }
            catch
            {
                // 忽略预加载错误
            }
        }
    }
}
