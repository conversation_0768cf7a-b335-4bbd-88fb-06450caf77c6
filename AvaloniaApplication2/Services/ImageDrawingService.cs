using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;
using AvaloniaApplication2.Models;
using AvaloniaApplication2.ROI.Engine;
using AvaloniaApplication2.ROI;
using AvaloniaApplication2.ROI.Shapes;
using AvaloniaApplication2.ROI.Tools;

namespace AvaloniaApplication2.Services
{
    public enum EditHandleKind { None, Move, Rotate, N, S, E, W, NE, NW, SE, SW }

    public class ImageDrawingService
    {
        public RoiEditorEngine RoiEngine { get; } = new();
        // Simple undo stack for basic actions
        private readonly Stack<Action> _undoStack = new();
        public ToolMode Mode { get; private set; } = ToolMode.Pan;

        private IBrush _stroke = Brushes.Lime;
        public IBrush Stroke
        {
            get => _stroke;
            set
            {
                if (_stroke == value) return;
                _stroke = value ?? Brushes.Lime;
                // 同步到引擎默认样式
                RoiEngine.DefaultStroke = _stroke;
                // 立即应用到当前选中 ROI
                var sel = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
                if (sel != null)
                {
                    sel.Stroke = _stroke;
                }
                InvalidateRequested?.Invoke();
            }
        }

        private double _strokeThickness = 1.0;
        public double StrokeThickness
        {
            get => _strokeThickness;
            set
            {
                if (Math.Abs(_strokeThickness - value) < 0.0001) return;
                _strokeThickness = Math.Max(0.1, value);
                // 同步到引擎默认样式
                RoiEngine.DefaultStrokeThickness = _strokeThickness;
                // 立即应用到当前选中 ROI
                var sel = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
                if (sel != null)
                {
                    sel.StrokeThickness = _strokeThickness;
                }
                InvalidateRequested?.Invoke();
            }
        }

        // Per-image ROI storage - 每张图片对应一套 ROI 形状
        private readonly Dictionary<string, List<RoiShape>> _roiStore = new();
        private string _currentImageKey = string.Empty;

        // External hooks to pan the main ScrollViewer
        public Func<Vector, bool>? RequestPanBy; // returns true if handled
        public Action? InvalidateRequested; // notify view to refresh visuals if needed
        public Action<string>? GalleryRefreshRequested; // 通知图库刷新当前图片缩略图

        public ImageDrawingService()
        {
            // 订阅引擎形状变更，驱动视图重绘与图库刷新
            RoiEngine.ShapesChanged += () =>
            {
                InvalidateRequested?.Invoke();
                if (!string.IsNullOrWhiteSpace(_currentImageKey))
                    GalleryRefreshRequested?.Invoke(_currentImageKey);
            };
        }

        // simple UI-thread throttle for redraw to boost responsiveness
        private DateTime _lastInvalidateTs = DateTime.MinValue;
        private void RequestInvalidateThrottled(int minMs = 12)
        {
            var now = DateTime.UtcNow;
            if ((now - _lastInvalidateTs).TotalMilliseconds >= minMs)
            {
                _lastInvalidateTs = now;
                InvalidateRequested?.Invoke();
            }
        }

        public void SetMode(ToolMode mode)
        {
            Mode = mode;

            // Map to new engine tools
            switch (Mode)
            {
                case ToolMode.Rectangle:
                    RoiEngine.SetTool(new ToolRectangle());
                    break;
                case ToolMode.Circle:
                    // For now, use the same tool as rectangle since both are closed shapes
                    RoiEngine.SetTool(new ToolRectangle());
                    break;
                case ToolMode.Line:
                    // For now, use the polygon tool but with a special flag for line mode
                    RoiEngine.SetTool(new ToolPolygon());
                    break;
                case ToolMode.Pencil:
                case ToolMode.Polygon:
                    RoiEngine.SetTool(new ToolPolygon());
                    break;
                case ToolMode.Pan:
                default:
                    RoiEngine.SetTool(new ToolPointer());
                    break;
            }
        }

        public void ResetState()
        {
            // 重置所有临时状态 - 现在只需要重置引擎状态
            // 引擎内部会处理工具状态的重置
        }

        public void SwitchImage(string key)
        {
            // 保存当前图像的形状
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                _roiStore[_currentImageKey] = RoiEngine.Shapes.ToList();
            }

            // 切换到新图像并恢复该图像的形状
            _currentImageKey = key ?? string.Empty;

            // 清空当前引擎中的所有形状
            RoiEngine.Shapes.Clear();

            // 恢复新图像对应的形状
            if (!string.IsNullOrWhiteSpace(_currentImageKey) && _roiStore.TryGetValue(_currentImageKey, out var list))
            {
                foreach (var s in list)
                    RoiEngine.Shapes.Add(s);
            }

            // 重置所有状态
            ResetState();
            RequestInvalidateThrottled();
        }

        public void SetImageBounds(Rect bounds)
        {
            RoiEngine.ImageBounds = bounds;
        }

        public void DeleteSelected()
        {
            var target = RoiEngine.Shapes.LastOrDefault(r => r.IsSelected);
            if (target != null)
            {
                var idx = RoiEngine.Shapes.IndexOf(target);
                _undoStack.Push(() =>
                {
                    if (idx >= 0 && idx <= RoiEngine.Shapes.Count) RoiEngine.Shapes.Insert(idx, target);
                    foreach (var r in RoiEngine.Shapes) r.IsSelected = false;
                    target.IsSelected = true;
                    RequestInvalidateThrottled();
                });
                RoiEngine.Shapes.Remove(target);
                RequestInvalidateThrottled();
            }
        }

        /// <summary>
        /// Clears all shapes from the current image
        /// </summary>
        public void ClearShapes()
        {
            if (RoiEngine.Shapes.Count == 0) return;
            
            // Save current shapes for undo
            var shapesToRemove = RoiEngine.Shapes.ToList();
            _undoStack.Push(() =>
            {
                foreach (var shape in shapesToRemove)
                {
                    RoiEngine.Shapes.Add(shape);
                }
                RequestInvalidateThrottled();
            });
            
            // Clear all shapes
            RoiEngine.Shapes.Clear();
            // Also clear stored shapes for current image to avoid reappearing on image switch
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                _roiStore[_currentImageKey] = new List<RoiShape>();
            }
            // Reset transient state
            ResetState();
            RequestInvalidateThrottled();
        }

        public void Undo()
        {
            if (_undoStack.Count > 0)
            {
                var act = _undoStack.Pop();
                act?.Invoke();
            }
        }

        public void PointerPressed(Point imagePt, PointerPointProperties props, int clickCount, bool isRightClick)
        {
            RoiEngine.PointerPressed(imagePt, props, clickCount, isRightClick);
            InvalidateRequested?.Invoke();
        }

        public void PointerMoved(Point imagePt, bool isLeftDown)
        {
            RoiEngine.PointerMoved(imagePt, isLeftDown);
            RequestInvalidateThrottled();
        }

        public void PointerReleased(Point imagePt)
        {
            RoiEngine.PointerReleased(imagePt);
            RequestInvalidateThrottled();
        }

        public void CompletePolygon()
        {
            // 多边形完成逻辑现在由 ROI 引擎内部处理
            // 这里可以添加额外的完成后处理逻辑
            RequestInvalidateThrottled();
        }

        public void CancelActive()
        {
            // 取消当前活动操作 - 现在由 ROI 引擎处理
            // 可以在这里添加额外的取消逻辑
            InvalidateRequested?.Invoke();
        }

        // ===== 光标处理方法 =====
        public EditHandleKind GetHoverHandleKind(Point imagePt)
        {
            // 优先：对已选中项做更精细的手柄判断；否则：取最上层命中项
            var topSelected = RoiEngine.Shapes.LastOrDefault(s => s.IsSelected);
            RoiShape? target = topSelected ?? RoiEngine.Shapes.LastOrDefault(s => s.HitTest(imagePt) >= 0);
            if (target == null) return EditHandleKind.None;

            if (target is RoiRectangle r)
            {
                int h = r.HitTest(imagePt);
                return h switch
                {
                    0 => EditHandleKind.Move,
                    1 => EditHandleKind.NW,
                    2 => EditHandleKind.N,
                    3 => EditHandleKind.NE,
                    4 => EditHandleKind.E,
                    5 => EditHandleKind.SE,
                    6 => EditHandleKind.S,
                    7 => EditHandleKind.SW,
                    8 => EditHandleKind.W,
                    9 => EditHandleKind.Rotate,
                    _ => EditHandleKind.None
                };
            }
            if (target is RoiPolygon pg)
            {
                int h = pg.HitTest(imagePt);
                if (h == 0) return EditHandleKind.Move; // edge/inside
                if (h > 0) return EditHandleKind.Move; // vertex drag uses move cursor
                return EditHandleKind.None;
            }
            return EditHandleKind.None;
        }
    }
}
