using System;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;
using AvaloniaApplication2.Models;

namespace AvaloniaApplication2.Services
{
    /// <summary>
    /// 简化的图像绘制服务，完全基于 RenderView 控件实现
    /// 移除了所有旧的 ROI 系统依赖
    /// </summary>
    public class ImageDrawingService
    {
        public ToolMode Mode { get; private set; } = ToolMode.Pan;

        // 保持与原有接口的兼容性
        public IBrush Stroke { get; set; } = Brushes.Lime;
        public double StrokeThickness { get; set; } = 1.0;

        // External hooks
        public Func<Vector, bool>? RequestPanBy; // returns true if handled
        public Action? InvalidateRequested; // notify view to refresh visuals if needed
        public Action<string>? GalleryRefreshRequested; // 通知图库刷新当前图片缩略图

        private string _currentImageKey = string.Empty;

        public ImageDrawingService()
        {
            // 构造函数现在很简单，不需要复杂的初始化
        }

        public void SetMode(ToolMode mode)
        {
            Mode = mode;
            // 模式设置现在由 RenderView 控件处理
            // 这里只需要记录当前模式
        }

        public void ResetState()
        {
            // 状态重置现在由 RenderView 控件内部处理
            // 这里保持接口兼容性
        }

        public void SwitchImage(string key)
        {
            // 切换图像时的处理
            _currentImageKey = key ?? string.Empty;
            
            // 重置状态
            ResetState();
            
            // 通知视图刷新
            InvalidateRequested?.Invoke();
        }

        public void SetImageBounds(Rect bounds)
        {
            // 图像边界设置现在由 RenderView 控件处理
            // 保持接口兼容性
        }

        public void DeleteSelected()
        {
            // 删除选中形状的逻辑现在由 RenderView 控件处理
            // 这里可以添加额外的处理逻辑
            InvalidateRequested?.Invoke();
        }

        public void ClearShapes()
        {
            // 清空所有形状的逻辑现在由 RenderView 控件处理
            // 这里可以添加额外的处理逻辑
            InvalidateRequested?.Invoke();
        }

        public void Undo()
        {
            // 撤销操作现在由 RenderView 控件处理
            // 这里可以添加额外的处理逻辑
            InvalidateRequested?.Invoke();
        }

        public void PointerPressed(Point imagePt, PointerPointProperties props, int clickCount, bool isRightClick)
        {
            // 鼠标按下事件现在由 RenderView 控件处理
            // 这里保持接口兼容性
            InvalidateRequested?.Invoke();
        }

        public void PointerMoved(Point imagePt, bool isLeftDown)
        {
            // 鼠标移动事件现在由 RenderView 控件处理
            // 这里保持接口兼容性
        }

        public void PointerReleased(Point imagePt)
        {
            // 鼠标释放事件现在由 RenderView 控件处理
            // 这里保持接口兼容性
            InvalidateRequested?.Invoke();
        }

        public void CompletePolygon()
        {
            // 多边形完成逻辑现在由 RenderView 控件处理
            // 这里保持接口兼容性
            InvalidateRequested?.Invoke();
        }

        public void CancelActive()
        {
            // 取消当前活动操作现在由 RenderView 控件处理
            // 这里保持接口兼容性
            InvalidateRequested?.Invoke();
        }

        /// <summary>
        /// 获取鼠标悬停时的手柄类型
        /// 现在由 RenderView 控件内部处理，这里返回默认值保持兼容性
        /// </summary>
        public EditHandleKind GetHoverHandleKind(Point imagePt)
        {
            // 光标处理现在由 RenderView 控件内部处理
            // 这里返回默认值保持接口兼容性
            return EditHandleKind.None;
        }
    }

    /// <summary>
    /// 编辑手柄类型枚举，保持与原有代码的兼容性
    /// </summary>
    public enum EditHandleKind 
    { 
        None, 
        Move, 
        Rotate, 
        N, 
        S, 
        E, 
        W, 
        NE, 
        NW, 
        SE, 
        SW 
    }
}
