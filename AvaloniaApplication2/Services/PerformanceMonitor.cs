using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;

namespace AvaloniaApplication2.Services
{
    /// <summary>
    /// 性能监控服务
    /// </summary>
    public class PerformanceMonitor
    {
        private static readonly Lazy<PerformanceMonitor> _instance = new(() => new PerformanceMonitor());
        public static PerformanceMonitor Instance => _instance.Value;

        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics = new();
        private readonly Timer _memoryMonitorTimer;

        public PerformanceMonitor()
        {
            // 每30秒监控一次内存使用情况
            _memoryMonitorTimer = new Timer(MonitorMemory, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        public IDisposable StartTiming(string operationName)
        {
            return new TimingScope(operationName, this);
        }

        /// <summary>
        /// 记录操作时间
        /// </summary>
        public void RecordTiming(string operationName, TimeSpan duration)
        {
            _metrics.AddOrUpdate(operationName, 
                new PerformanceMetric(operationName, duration),
                (key, existing) => existing.AddSample(duration));
        }

        /// <summary>
        /// 记录内存使用情况
        /// </summary>
        public void RecordMemoryUsage(string context, long memoryBytes)
        {
            var metricName = $"Memory_{context}";
            _metrics.AddOrUpdate(metricName,
                new PerformanceMetric(metricName, TimeSpan.FromMilliseconds(memoryBytes / 1024.0 / 1024.0)), // 转换为MB
                (key, existing) => existing.AddSample(TimeSpan.FromMilliseconds(memoryBytes / 1024.0 / 1024.0)));
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        public string GetPerformanceReport()
        {
            var report = "=== 性能监控报告 ===\n";
            
            foreach (var metric in _metrics.Values)
            {
                report += $"{metric.Name}:\n";
                report += $"  样本数: {metric.SampleCount}\n";
                report += $"  平均值: {metric.AverageTime.TotalMilliseconds:F2}ms\n";
                report += $"  最小值: {metric.MinTime.TotalMilliseconds:F2}ms\n";
                report += $"  最大值: {metric.MaxTime.TotalMilliseconds:F2}ms\n";
                report += "\n";
            }

            // 添加当前内存使用情况
            var process = Process.GetCurrentProcess();
            report += $"当前内存使用:\n";
            report += $"  工作集: {process.WorkingSet64 / 1024.0 / 1024.0:F2} MB\n";
            report += $"  私有内存: {process.PrivateMemorySize64 / 1024.0 / 1024.0:F2} MB\n";
            report += $"  GC内存: {GC.GetTotalMemory(false) / 1024.0 / 1024.0:F2} MB\n";

            return report;
        }

        /// <summary>
        /// 清除所有统计信息
        /// </summary>
        public void ClearMetrics()
        {
            _metrics.Clear();
        }

        private void MonitorMemory(object? state)
        {
            try
            {
                var process = Process.GetCurrentProcess();
                RecordMemoryUsage("WorkingSet", process.WorkingSet64);
                RecordMemoryUsage("PrivateMemory", process.PrivateMemorySize64);
                RecordMemoryUsage("GCMemory", GC.GetTotalMemory(false));
            }
            catch
            {
                // 忽略监控错误
            }
        }

        private class TimingScope : IDisposable
        {
            private readonly string _operationName;
            private readonly PerformanceMonitor _monitor;
            private readonly Stopwatch _stopwatch;

            public TimingScope(string operationName, PerformanceMonitor monitor)
            {
                _operationName = operationName;
                _monitor = monitor;
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                _stopwatch.Stop();
                _monitor.RecordTiming(_operationName, _stopwatch.Elapsed);
            }
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        private readonly object _lock = new object();
        private TimeSpan _totalTime;
        private int _sampleCount;
        private TimeSpan _minTime = TimeSpan.MaxValue;
        private TimeSpan _maxTime = TimeSpan.MinValue;

        public string Name { get; }
        public TimeSpan AverageTime => _sampleCount > 0 ? TimeSpan.FromTicks(_totalTime.Ticks / _sampleCount) : TimeSpan.Zero;
        public TimeSpan MinTime => _minTime == TimeSpan.MaxValue ? TimeSpan.Zero : _minTime;
        public TimeSpan MaxTime => _maxTime == TimeSpan.MinValue ? TimeSpan.Zero : _maxTime;
        public int SampleCount => _sampleCount;

        public PerformanceMetric(string name, TimeSpan initialSample)
        {
            Name = name;
            AddSample(initialSample);
        }

        public PerformanceMetric AddSample(TimeSpan sample)
        {
            lock (_lock)
            {
                _totalTime = _totalTime.Add(sample);
                _sampleCount++;
                
                if (sample < _minTime)
                    _minTime = sample;
                
                if (sample > _maxTime)
                    _maxTime = sample;
            }
            
            return this;
        }
    }
}
