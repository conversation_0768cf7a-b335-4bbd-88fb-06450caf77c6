using System;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AvaloniaApplication2.Models;

namespace AvaloniaApplication2.ViewModels
{
    public partial class SplitViewModel : ViewModelBase
    {
        // Reference to gallery for images/tags source
        [ObservableProperty]
        private GalleryViewModel? _galleryRef;

        // Open the create split window (handled in view code-behind)
        [ObservableProperty]
        private bool _requestOpenCreateSplit;

        // Create-split parameters
        [ObservableProperty]
        private string _splitName = $"拆分-{DateTime.Now:yyMMdd-HHmmss}";

        [ObservableProperty]
        private int _trainPct = 70;
        [ObservableProperty]
        private int _valPct = 15;
        [ObservableProperty]
        private int _testPct = 15;

        [ObservableProperty]
        private bool _useCustomSeed = false;
        [ObservableProperty]
        private int _seed = 1;

        // Totals derived from current tags
        [ObservableProperty]
        private int _totalAll;
        [ObservableProperty]
        private int _totalGood;
        [ObservableProperty]
        private int _totalAnomaly;

        // Summary after applying split
        [ObservableProperty]
        private bool _hasActiveSplit;

        [ObservableProperty]
        private int _trainGood;
        [ObservableProperty]
        private int _trainAnomaly;
        [ObservableProperty]
        private int _valGood;
        [ObservableProperty]
        private int _valAnomaly;
        [ObservableProperty]
        private int _testGood;
        [ObservableProperty]
        private int _testAnomaly;

        public SplitViewModel()
        {
        }

        public void RefreshTotals()
        {
            if (GalleryRef == null) { TotalAll = TotalGood = TotalAnomaly = 0; return; }
            var tags = GalleryRef.Tags.ToDictionary(t => t.Id);
            var items = GalleryRef.GalleryItems;
            int good = 0, anomaly = 0, all = items.Count;
            foreach (var it in items)
            {
                if (!tags.TryGetValue(it.TagId, out var tag)) continue;
                if (string.Equals(tag.IconKind, "ThumbUp", StringComparison.OrdinalIgnoreCase)) good++;
                else if (string.Equals(tag.IconKind, "ThumbDown", StringComparison.OrdinalIgnoreCase)) anomaly++;
            }
            TotalAll = all;
            TotalGood = good;
            TotalAnomaly = anomaly;
        }

        private static int RoundByPct(int total, int pct) => (int)Math.Round(total * (pct / 100.0));

        private void ClampPercents()
        {
            // keep sum near 100 by adjusting test
            int sum = TrainPct + ValPct + TestPct;
            if (sum != 100)
            {
                TestPct = Math.Max(0, 100 - TrainPct - ValPct);
            }
        }

        [RelayCommand]
        private void OpenCreateSplit()
        {
            RefreshTotals();
            RequestOpenCreateSplit = true; // view should reset it to false after opening
        }

        [RelayCommand]
        private void HideCreateSplit()
        {
            RequestOpenCreateSplit = false;
        }

        [RelayCommand]
        private void IncreaseTrain() { TrainPct = Math.Min(100, TrainPct + 1); ClampPercents(); }
        [RelayCommand]
        private void DecreaseTrain() { TrainPct = Math.Max(0, TrainPct - 1); ClampPercents(); }
        [RelayCommand]
        private void IncreaseVal() { ValPct = Math.Min(100, ValPct + 1); ClampPercents(); }
        [RelayCommand]
        private void DecreaseVal() { ValPct = Math.Max(0, ValPct - 1); ClampPercents(); }
        [RelayCommand]
        private void IncreaseTest() { TestPct = Math.Min(100, TestPct + 1); ClampPercents(); }
        [RelayCommand]
        private void DecreaseTest() { TestPct = Math.Max(0, TestPct - 1); ClampPercents(); }

        [RelayCommand]
        private void ApplySplitAndAssign()
        {
            // Compute counts by class proportionally
            RefreshTotals();
            var rng = UseCustomSeed ? new Random(Seed) : new Random();
            // For now, we only compute numbers; actual per-image allocation can be added later
            TrainGood = RoundByPct(TotalGood, TrainPct);
            ValGood = RoundByPct(TotalGood, ValPct);
            TestGood = TotalGood - TrainGood - ValGood;

            TrainAnomaly = RoundByPct(TotalAnomaly, TrainPct);
            ValAnomaly = RoundByPct(TotalAnomaly, ValPct);
            TestAnomaly = TotalAnomaly - TrainAnomaly - ValAnomaly;

            HasActiveSplit = true;
        }

        [RelayCommand]
        private void RefreshTotalsCommand() => RefreshTotals();

        [RelayCommand]
        private void IncreaseSeed() { Seed++; }
        [RelayCommand]
        private void DecreaseSeed() { if (Seed > 0) Seed--; }
    }
}
