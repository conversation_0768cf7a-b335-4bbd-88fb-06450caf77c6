from ultralytics import YOLO
from pathlib import Path
import json
import cv2

def test_batch_images(model_path, test_dir, output_dir, conf_threshold=0.25):
    """批量测试图像"""
    model = YOLO(model_path)
    test_path = Path(test_dir)
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 支持的图像格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    # 获取所有图像文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(test_path.glob(f'*{ext}'))
        image_files.extend(test_path.glob(f'*{ext.upper()}'))
    
    print(f"找到 {len(image_files)} 张图像")
    
    # 批量预测
    results = model(
        [str(img) for img in image_files],
        conf=conf_threshold,
        iou=0.5,
        imgsz=640,
        device=0,
        stream=True  # 流式处理，节省内存
    )
    
    # 处理结果
    all_results = []
    for i, (result, img_file) in enumerate(zip(results, image_files)):
        print(f"处理 {i+1}/{len(image_files)}: {img_file.name}")
        
        # 保存可视化结果
        annotated_img = result.plot()
        result_path = output_path / f"result_{img_file.name}"
        cv2.imwrite(str(result_path), annotated_img)
        
        # 收集检测结果
        detections = []
        for box in result.boxes:
            detection = {
                'class_id': int(box.cls[0]),
                'class_name': model.names[int(box.cls[0])],
                'confidence': float(box.conf[0]),
                'bbox': box.xyxy[0].tolist()
            }
            detections.append(detection)
        
        all_results.append({
            'image': img_file.name,
            'detections': detections,
            'count': len(detections)
        })
    
    # 保存JSON结果
    with open(output_path / 'results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"测试完成，结果保存在: {output_path}")
    return all_results

# 使用示例
test_batch_images(
    model_path='runs/detect/train/weights/best.pt',
    test_dir='test_images',
    output_dir='test_results',
    conf_threshold=0.3
)